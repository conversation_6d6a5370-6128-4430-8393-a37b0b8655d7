import mysql from 'mysql2/promise'
import { config } from '../config/database.js'
import logger from '../utils/logger.js'

class DatabaseConnection {
  private pool: mysql.Pool | null = null
  private isConnected = false

  constructor() {
    this.initializePool()
  }

  private initializePool(): void {
    try {
      this.pool = mysql.createPool({
        host: config.host,
        port: config.port,
        user: config.user,
        password: config.password,
        database: config.database,
        waitForConnections: true,
        connectionLimit: 10,
        queueLimit: 0,
        acquireTimeout: 60000,
        timeout: 60000,
        reconnect: true,
        charset: 'utf8mb4',
        timezone: '+00:00',
      })

      logger.info('Database pool initialized')
    } catch (error) {
      logger.error('Failed to initialize database pool:', error)
      throw error
    }
  }

  async connect(): Promise<void> {
    if (this.isConnected) {
      return
    }

    try {
      if (!this.pool) {
        throw new Error('Database pool not initialized')
      }

      // Test the connection
      const connection = await this.pool.getConnection()
      await connection.ping()
      connection.release()

      this.isConnected = true
      logger.info('Database connected successfully')
    } catch (error) {
      logger.error('Database connection failed:', error)
      throw error
    }
  }

  async disconnect(): Promise<void> {
    if (this.pool) {
      await this.pool.end()
      this.pool = null
      this.isConnected = false
      logger.info('Database disconnected')
    }
  }

  async query<T = any>(sql: string, params?: any[]): Promise<T[]> {
    if (!this.pool) {
      throw new Error('Database not connected')
    }

    try {
      const [rows] = await this.pool.execute(sql, params)
      return rows as T[]
    } catch (error) {
      logger.error('Database query error:', { sql, params, error })
      throw error
    }
  }

  async queryOne<T = any>(sql: string, params?: any[]): Promise<T | null> {
    const results = await this.query<T>(sql, params)
    return results.length > 0 ? results[0] : null
  }

  async transaction<T>(callback: (connection: mysql.PoolConnection) => Promise<T>): Promise<T> {
    if (!this.pool) {
      throw new Error('Database not connected')
    }

    const connection = await this.pool.getConnection()
    
    try {
      await connection.beginTransaction()
      const result = await callback(connection)
      await connection.commit()
      return result
    } catch (error) {
      await connection.rollback()
      throw error
    } finally {
      connection.release()
    }
  }

  async healthCheck(): Promise<boolean> {
    try {
      await this.query('SELECT 1')
      return true
    } catch (error) {
      logger.error('Database health check failed:', error)
      return false
    }
  }

  getConnectionStatus(): boolean {
    return this.isConnected
  }
}

// Singleton instance
export const db = new DatabaseConnection()
export default db
