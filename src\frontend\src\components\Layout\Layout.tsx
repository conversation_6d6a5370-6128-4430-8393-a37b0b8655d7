import React from 'react'
import Sidebar from './Sidebar'
import Header from './Header'

interface LayoutProps {
  children: React.ReactNode
}

const Layout: React.FC<LayoutProps> = ({ children }) => {
  return (
    <>
      <Header />
      <div className="layout-main">
        <Sidebar />
        <main className="layout-content">
          {children}
        </main>
      </div>
    </>
  )
}

export default Layout
