import { M3UEntry } from '../../types/database.js'
import logger from '../utils/logger.js'

export class M3UParser {
  private static readonly MOVIE_PATTERNS = [
    /\b(19|20)\d{2}\b/, // Year pattern
    /\.(mp4|mkv|avi|mov|wmv|flv|webm)$/i, // Video file extensions
    /\b(movie|film|cinema)\b/i, // Movie keywords
  ]

  private static readonly SERIES_PATTERNS = [
    /S\d{1,2}E\d{1,2}/i, // Season Episode pattern (S01E01)
    /Season\s*\d+/i, // Season pattern
    /Episode\s*\d+/i, // Episode pattern
    /\d{1,2}x\d{1,2}/i, // Alternative episode pattern (1x01)
  ]

  private static readonly LIVE_PATTERNS = [
    /\b(live|tv|channel|hd|sd)\b/i,
    /\b(news|sport|music|kids)\b/i,
    /tvg-id=/i, // EPG identifier
  ]

  static parseM3U(content: string): M3UEntry[] {
    const lines = content.split('\n').map(line => line.trim()).filter(line => line)
    const entries: M3UEntry[] = []
    
    let currentEntry: Partial<M3UEntry> = {}
    
    for (let i = 0; i < lines.length; i++) {
      const line = lines[i]
      
      if (line.startsWith('#EXTINF:')) {
        // Parse EXTINF line
        currentEntry = this.parseExtinf(line)
      } else if (line.startsWith('http://') || line.startsWith('https://')) {
        // This is a URL line
        if (currentEntry.title) {
          currentEntry.url = line
          
          // Determine content type
          const type = this.determineContentType(currentEntry as M3UEntry)
          currentEntry.type = type
          
          // Extract additional metadata based on type
          if (type === 'series') {
            const episodeInfo = this.extractEpisodeInfo(currentEntry.title!)
            currentEntry.season = episodeInfo.season
            currentEntry.episode = episodeInfo.episode
          } else if (type === 'movie') {
            const year = this.extractYear(currentEntry.title!)
            if (year) currentEntry.year = year
          }
          
          entries.push(currentEntry as M3UEntry)
        }
        currentEntry = {}
      }
    }
    
    logger.info(`Parsed ${entries.length} entries from M3U playlist`)
    return entries
  }

  private static parseExtinf(line: string): Partial<M3UEntry> {
    const entry: Partial<M3UEntry> = {}
    
    // Extract title (everything after the last comma)
    const titleMatch = line.match(/,(.+)$/)
    if (titleMatch) {
      entry.title = titleMatch[1].trim()
    }
    
    // Extract tvg-id
    const tvgIdMatch = line.match(/tvg-id="([^"]*)"/)
    if (tvgIdMatch) {
      entry.tvg_id = tvgIdMatch[1]
    }
    
    // Extract tvg-name
    const tvgNameMatch = line.match(/tvg-name="([^"]*)"/)
    if (tvgNameMatch) {
      entry.tvg_name = tvgNameMatch[1]
    }
    
    // Extract logo
    const logoMatch = line.match(/tvg-logo="([^"]*)"/)
    if (logoMatch) {
      entry.logo = logoMatch[1]
    }
    
    // Extract group
    const groupMatch = line.match(/group-title="([^"]*)"/)
    if (groupMatch) {
      entry.group = groupMatch[1]
    }
    
    return entry
  }

  private static determineContentType(entry: M3UEntry): 'movie' | 'series' | 'live' {
    const title = entry.title.toLowerCase()
    const url = entry.url.toLowerCase()
    
    // Check for series patterns first
    if (this.SERIES_PATTERNS.some(pattern => pattern.test(entry.title))) {
      return 'series'
    }
    
    // Check for movie patterns
    if (this.MOVIE_PATTERNS.some(pattern => pattern.test(entry.title)) ||
        this.MOVIE_PATTERNS.some(pattern => pattern.test(entry.url))) {
      return 'movie'
    }
    
    // Check for live TV patterns
    if (this.LIVE_PATTERNS.some(pattern => pattern.test(entry.title)) ||
        entry.tvg_id || 
        entry.group?.toLowerCase().includes('live') ||
        url.includes('/live/') ||
        url.includes('/channel/')) {
      return 'live'
    }
    
    // Default classification based on URL structure
    if (url.includes('/movie/') || url.includes('/movies/')) {
      return 'movie'
    }
    
    if (url.includes('/series/') || url.includes('/episode/')) {
      return 'series'
    }
    
    // Default to live if uncertain
    return 'live'
  }

  private static extractEpisodeInfo(title: string): { season?: number; episode?: number } {
    // Try S01E01 pattern
    const seasonEpisodeMatch = title.match(/S(\d{1,2})E(\d{1,2})/i)
    if (seasonEpisodeMatch) {
      return {
        season: parseInt(seasonEpisodeMatch[1]),
        episode: parseInt(seasonEpisodeMatch[2])
      }
    }
    
    // Try 1x01 pattern
    const altPatternMatch = title.match(/(\d{1,2})x(\d{1,2})/)
    if (altPatternMatch) {
      return {
        season: parseInt(altPatternMatch[1]),
        episode: parseInt(altPatternMatch[2])
      }
    }
    
    // Try separate Season and Episode patterns
    const seasonMatch = title.match(/Season\s*(\d+)/i)
    const episodeMatch = title.match(/Episode\s*(\d+)/i)
    
    const result: { season?: number; episode?: number } = {}
    
    if (seasonMatch) {
      result.season = parseInt(seasonMatch[1])
    }
    
    if (episodeMatch) {
      result.episode = parseInt(episodeMatch[1])
    }
    
    return result
  }

  private static extractYear(title: string): number | undefined {
    const yearMatch = title.match(/\b(19|20)\d{2}\b/)
    return yearMatch ? parseInt(yearMatch[0]) : undefined
  }

  // Classify entries by type
  static classifyEntries(entries: M3UEntry[]): {
    movies: M3UEntry[];
    series: M3UEntry[];
    live: M3UEntry[];
  } {
    const movies = entries.filter(entry => entry.type === 'movie')
    const series = entries.filter(entry => entry.type === 'series')
    const live = entries.filter(entry => entry.type === 'live')
    
    logger.info(`Classified entries: ${movies.length} movies, ${series.length} series, ${live.length} live channels`)
    
    return { movies, series, live }
  }

  // Group series entries by series name
  static groupSeriesEntries(seriesEntries: M3UEntry[]): Map<string, M3UEntry[]> {
    const seriesMap = new Map<string, M3UEntry[]>()
    
    for (const entry of seriesEntries) {
      // Extract series name (remove season/episode info)
      let seriesName = entry.title
        .replace(/S\d{1,2}E\d{1,2}/gi, '')
        .replace(/Season\s*\d+/gi, '')
        .replace(/Episode\s*\d+/gi, '')
        .replace(/\d{1,2}x\d{1,2}/gi, '')
        .replace(/\s+/g, ' ')
        .trim()
      
      // Remove year if present
      seriesName = seriesName.replace(/\b(19|20)\d{2}\b/, '').trim()
      
      if (!seriesMap.has(seriesName)) {
        seriesMap.set(seriesName, [])
      }
      
      seriesMap.get(seriesName)!.push(entry)
    }
    
    return seriesMap
  }

  // Validate M3U content
  static validateM3U(content: string): { isValid: boolean; errors: string[] } {
    const errors: string[] = []
    
    if (!content.trim()) {
      errors.push('M3U content is empty')
      return { isValid: false, errors }
    }
    
    if (!content.includes('#EXTM3U')) {
      errors.push('Missing #EXTM3U header')
    }
    
    const lines = content.split('\n')
    const extinfLines = lines.filter(line => line.startsWith('#EXTINF:'))
    const urlLines = lines.filter(line => line.startsWith('http://') || line.startsWith('https://'))
    
    if (extinfLines.length === 0) {
      errors.push('No #EXTINF entries found')
    }
    
    if (urlLines.length === 0) {
      errors.push('No URLs found')
    }
    
    if (Math.abs(extinfLines.length - urlLines.length) > 1) {
      errors.push('Mismatch between #EXTINF entries and URLs')
    }
    
    return {
      isValid: errors.length === 0,
      errors
    }
  }
}
