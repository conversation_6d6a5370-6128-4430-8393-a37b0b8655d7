import { db } from '../database/connection.js'
import { StreamSeries, StreamEpisode, SeriesInfo, EpisodeInfo } from '../../types/database.js'
import logger from '../utils/logger.js'

export class SeriesModel {
  // Get all series with episode counts
  static async getAllSeries(): Promise<SeriesInfo[]> {
    const sql = `
      SELECT 
        ss.*,
        COUNT(se.id) as episode_count,
        COUNT(DISTINCT se.season_num) as season_count
      FROM streams_series ss
      LEFT JOIN streams_episodes se ON ss.id = se.series_id
      GROUP BY ss.id
      ORDER BY ss.title
    `
    
    const series = await db.query<SeriesInfo>(sql)
    
    // Calculate missing episodes and other stats for each series
    for (const serie of series) {
      const episodeStats = await this.getSeriesEpisodeStats(serie.id)
      serie.missing_episodes = episodeStats.missing_episodes
      serie.orphan_episodes = episodeStats.orphan_episodes
      serie.complete_seasons = episodeStats.complete_seasons
      serie.incomplete_seasons = episodeStats.incomplete_seasons
    }
    
    return series
  }

  // Get series by ID with detailed episode information
  static async getSeriesById(id: number): Promise<SeriesInfo | null> {
    const sql = `
      SELECT 
        ss.*,
        COUNT(se.id) as episode_count
      FROM streams_series ss
      LEFT JOIN streams_episodes se ON ss.id = se.series_id
      WHERE ss.id = ?
      GROUP BY ss.id
    `
    
    const series = await db.queryOne<SeriesInfo>(sql, [id])
    if (!series) return null
    
    const episodeStats = await this.getSeriesEpisodeStats(id)
    series.missing_episodes = episodeStats.missing_episodes
    series.orphan_episodes = episodeStats.orphan_episodes
    series.complete_seasons = episodeStats.complete_seasons
    series.incomplete_seasons = episodeStats.incomplete_seasons
    
    return series
  }

  // Get detailed episode statistics for a series
  private static async getSeriesEpisodeStats(seriesId: number): Promise<{
    missing_episodes: number[];
    orphan_episodes: StreamEpisode[];
    complete_seasons: number[];
    incomplete_seasons: number[];
  }> {
    // Get all episodes for this series
    const episodesSql = `
      SELECT se.*, s.stream_display_name
      FROM streams_episodes se
      LEFT JOIN streams s ON se.stream_id = s.id
      WHERE se.series_id = ?
      ORDER BY se.season_num, se.episode_num
    `
    
    const episodes = await db.query<EpisodeInfo>(episodesSql, [seriesId])
    
    // Group episodes by season
    const seasonMap = new Map<number, EpisodeInfo[]>()
    episodes.forEach(episode => {
      if (!seasonMap.has(episode.season_num)) {
        seasonMap.set(episode.season_num, [])
      }
      seasonMap.get(episode.season_num)!.push(episode)
    })
    
    const complete_seasons: number[] = []
    const incomplete_seasons: number[] = []
    const missing_episodes: number[] = []
    const orphan_episodes: StreamEpisode[] = []
    
    // Analyze each season
    for (const [seasonNum, seasonEpisodes] of seasonMap) {
      const episodeNumbers = seasonEpisodes.map(ep => ep.episode_num).sort((a, b) => a - b)
      const maxEpisode = Math.max(...episodeNumbers)
      
      // Check for missing episodes in sequence
      const missingInSeason: number[] = []
      for (let i = 1; i <= maxEpisode; i++) {
        if (!episodeNumbers.includes(i)) {
          missingInSeason.push(i)
          missing_episodes.push(i)
        }
      }
      
      // Check for orphan episodes (episodes without valid stream)
      const orphansInSeason = seasonEpisodes.filter(ep => !ep.stream_display_name)
      orphan_episodes.push(...orphansInSeason)
      
      // Determine if season is complete
      if (missingInSeason.length === 0 && orphansInSeason.length === 0) {
        complete_seasons.push(seasonNum)
      } else {
        incomplete_seasons.push(seasonNum)
      }
    }
    
    return {
      missing_episodes,
      orphan_episodes,
      complete_seasons,
      incomplete_seasons
    }
  }

  // Find series without any episodes
  static async getSeriesWithoutEpisodes(): Promise<StreamSeries[]> {
    const sql = `
      SELECT ss.*
      FROM streams_series ss
      LEFT JOIN streams_episodes se ON ss.id = se.series_id
      WHERE se.series_id IS NULL
      ORDER BY ss.title
    `
    return await db.query<StreamSeries>(sql)
  }

  // Find orphan episodes (episodes without a series)
  static async getOrphanEpisodes(): Promise<EpisodeInfo[]> {
    const sql = `
      SELECT 
        se.*,
        s.stream_display_name,
        'Unknown Series' as series_title
      FROM streams_episodes se
      LEFT JOIN streams_series ss ON se.series_id = ss.id
      LEFT JOIN streams s ON se.stream_id = s.id
      WHERE ss.id IS NULL
      ORDER BY se.season_num, se.episode_num
    `
    return await db.query<EpisodeInfo>(sql)
  }

  // Find duplicate episodes within the same series
  static async findDuplicateEpisodes(): Promise<{
    series_id: number;
    series_title: string;
    season_num: number;
    episode_num: number;
    duplicates: EpisodeInfo[];
  }[]> {
    const sql = `
      SELECT 
        se1.series_id,
        ss.title as series_title,
        se1.season_num,
        se1.episode_num,
        COUNT(*) as duplicate_count
      FROM streams_episodes se1
      JOIN streams_episodes se2 ON se1.series_id = se2.series_id 
        AND se1.season_num = se2.season_num 
        AND se1.episode_num = se2.episode_num
        AND se1.id != se2.id
      LEFT JOIN streams_series ss ON se1.series_id = ss.id
      GROUP BY se1.series_id, se1.season_num, se1.episode_num
      HAVING duplicate_count > 0
      ORDER BY ss.title, se1.season_num, se1.episode_num
    `
    
    const duplicateGroups = await db.query(sql)
    const result = []
    
    for (const group of duplicateGroups) {
      const episodesSql = `
        SELECT 
          se.*,
          s.stream_display_name,
          ss.title as series_title
        FROM streams_episodes se
        LEFT JOIN streams s ON se.stream_id = s.id
        LEFT JOIN streams_series ss ON se.series_id = ss.id
        WHERE se.series_id = ? AND se.season_num = ? AND se.episode_num = ?
        ORDER BY se.id
      `
      
      const duplicates = await db.query<EpisodeInfo>(episodesSql, [
        group.series_id,
        group.season_num,
        group.episode_num
      ])
      
      result.push({
        series_id: group.series_id,
        series_title: group.series_title,
        season_num: group.season_num,
        episode_num: group.episode_num,
        duplicates
      })
    }
    
    return result
  }

  // Delete series and all its episodes
  static async deleteSeries(id: number): Promise<boolean> {
    try {
      await db.transaction(async (connection) => {
        // Get all episode stream IDs first
        const episodeStreamIds = await connection.execute(
          'SELECT stream_id FROM streams_episodes WHERE series_id = ?',
          [id]
        )
        
        // Delete episode streams
        for (const row of episodeStreamIds[0] as any[]) {
          if (row.stream_id) {
            await connection.execute('DELETE FROM streams WHERE id = ?', [row.stream_id])
          }
        }
        
        // Delete episodes
        await connection.execute('DELETE FROM streams_episodes WHERE series_id = ?', [id])
        
        // Delete series
        await connection.execute('DELETE FROM streams_series WHERE id = ?', [id])
      })
      
      logger.info(`Series ${id} deleted successfully`)
      return true
    } catch (error) {
      logger.error(`Failed to delete series ${id}:`, error)
      return false
    }
  }

  // Delete orphan episodes
  static async deleteOrphanEpisodes(): Promise<number> {
    try {
      const orphans = await this.getOrphanEpisodes()
      let deletedCount = 0
      
      await db.transaction(async (connection) => {
        for (const orphan of orphans) {
          // Delete the stream if it exists
          if (orphan.stream_id) {
            await connection.execute('DELETE FROM streams WHERE id = ?', [orphan.stream_id])
          }
          
          // Delete the episode record
          await connection.execute('DELETE FROM streams_episodes WHERE id = ?', [orphan.id])
          deletedCount++
        }
      })
      
      logger.info(`Deleted ${deletedCount} orphan episodes`)
      return deletedCount
    } catch (error) {
      logger.error('Failed to delete orphan episodes:', error)
      return 0
    }
  }

  // Get series statistics for dashboard
  static async getSeriesStats(): Promise<{
    total_series: number;
    series_with_episodes: number;
    series_without_episodes: number;
    total_episodes: number;
    orphan_episodes: number;
    complete_series: number;
  }> {
    const sql = `
      SELECT 
        COUNT(DISTINCT ss.id) as total_series,
        COUNT(DISTINCT CASE WHEN se.series_id IS NOT NULL THEN ss.id END) as series_with_episodes,
        COUNT(DISTINCT CASE WHEN se.series_id IS NULL THEN ss.id END) as series_without_episodes,
        COUNT(se.id) as total_episodes,
        COUNT(CASE WHEN ss.id IS NULL THEN se.id END) as orphan_episodes
      FROM streams_series ss
      LEFT JOIN streams_episodes se ON ss.id = se.series_id
      
      UNION ALL
      
      SELECT 0, 0, 0, 0, COUNT(*), 0
      FROM streams_episodes se
      LEFT JOIN streams_series ss ON se.series_id = ss.id
      WHERE ss.id IS NULL
    `
    
    const result = await db.queryOne(sql)
    return result || {
      total_series: 0,
      series_with_episodes: 0,
      series_without_episodes: 0,
      total_episodes: 0,
      orphan_episodes: 0,
      complete_series: 0
    }
  }
}
