import { describe, it, expect, beforeAll, afterAll } from 'vitest'
import { db } from '../database/connection.js'

describe('Database Connection', () => {
  beforeAll(async () => {
    // Setup test database connection
    try {
      await db.connect()
    } catch (error) {
      console.warn('Database connection failed in tests - this is expected if DB is not configured')
    }
  })

  afterAll(async () => {
    await db.disconnect()
  })

  it('should have a database instance', () => {
    expect(db).toBeDefined()
  })

  it('should be able to check connection status', () => {
    const status = db.getConnectionStatus()
    expect(typeof status).toBe('boolean')
  })

  it('should handle health check gracefully', async () => {
    const health = await db.healthCheck()
    expect(typeof health).toBe('boolean')
  })
})
