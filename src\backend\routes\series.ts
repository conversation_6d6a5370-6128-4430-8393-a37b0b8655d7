import { Router } from 'express'
import { SeriesModel } from '../models/SeriesModel.js'
import logger from '../utils/logger.js'

const router = Router()

// Get all series
router.get('/', async (req, res) => {
  try {
    const series = await SeriesModel.getAllSeries()
    res.json(series)
  } catch (error) {
    logger.error('Error fetching series:', error)
    res.status(500).json({
      error: 'Failed to fetch series',
      message: error.message
    })
  }
})

// Get series by ID
router.get('/:id', async (req, res) => {
  try {
    const { id } = req.params
    const series = await SeriesModel.getSeriesById(Number(id))
    
    if (!series) {
      return res.status(404).json({
        error: 'Series not found',
        message: `Series with ID ${id} does not exist`
      })
    }
    
    res.json(series)
  } catch (error) {
    logger.error('Error fetching series:', error)
    res.status(500).json({
      error: 'Failed to fetch series',
      message: error.message
    })
  }
})

// Get series without episodes
router.get('/orphaned/without-episodes', async (req, res) => {
  try {
    const series = await SeriesModel.getSeriesWithoutEpisodes()
    res.json(series)
  } catch (error) {
    logger.error('Error fetching series without episodes:', error)
    res.status(500).json({
      error: 'Failed to fetch series without episodes',
      message: error.message
    })
  }
})

// Get orphan episodes
router.get('/orphaned/episodes', async (req, res) => {
  try {
    const episodes = await SeriesModel.getOrphanEpisodes()
    res.json(episodes)
  } catch (error) {
    logger.error('Error fetching orphan episodes:', error)
    res.status(500).json({
      error: 'Failed to fetch orphan episodes',
      message: error.message
    })
  }
})

// Find duplicate episodes
router.get('/duplicates/episodes', async (req, res) => {
  try {
    const duplicates = await SeriesModel.findDuplicateEpisodes()
    res.json(duplicates)
  } catch (error) {
    logger.error('Error finding duplicate episodes:', error)
    res.status(500).json({
      error: 'Failed to find duplicate episodes',
      message: error.message
    })
  }
})

// Delete series
router.delete('/:id', async (req, res) => {
  try {
    const { id } = req.params
    const success = await SeriesModel.deleteSeries(Number(id))
    
    if (success) {
      res.json({ message: 'Series deleted successfully' })
    } else {
      res.status(500).json({ error: 'Failed to delete series' })
    }
  } catch (error) {
    logger.error('Error deleting series:', error)
    res.status(500).json({
      error: 'Failed to delete series',
      message: error.message
    })
  }
})

// Delete orphan episodes
router.delete('/orphaned/episodes', async (req, res) => {
  try {
    const deletedCount = await SeriesModel.deleteOrphanEpisodes()
    res.json({
      message: 'Orphan episodes deleted successfully',
      deleted_count: deletedCount
    })
  } catch (error) {
    logger.error('Error deleting orphan episodes:', error)
    res.status(500).json({
      error: 'Failed to delete orphan episodes',
      message: error.message
    })
  }
})

// Bulk delete series
router.post('/bulk-delete', async (req, res) => {
  try {
    const { series_ids } = req.body
    
    if (!Array.isArray(series_ids) || series_ids.length === 0) {
      return res.status(400).json({
        error: 'Invalid request',
        message: 'series_ids must be a non-empty array'
      })
    }

    const results = []
    for (const id of series_ids) {
      const success = await SeriesModel.deleteSeries(Number(id))
      results.push({ id, success })
    }

    const successCount = results.filter(r => r.success).length
    const failureCount = results.length - successCount

    res.json({
      message: `Bulk delete completed`,
      results: {
        total: results.length,
        successful: successCount,
        failed: failureCount
      },
      details: results
    })
  } catch (error) {
    logger.error('Error in bulk delete:', error)
    res.status(500).json({
      error: 'Failed to perform bulk delete',
      message: error.message
    })
  }
})

export default router
