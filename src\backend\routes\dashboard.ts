import { Router } from 'express'
import { StreamModel } from '../models/StreamModel.js'
import { SeriesModel } from '../models/SeriesModel.js'
import logger from '../utils/logger.js'

const router = Router()

// Get dashboard statistics
router.get('/stats', async (req, res) => {
  try {
    const [streamStats, seriesStats, contentDistribution] = await Promise.all([
      StreamModel.getDashboardStats(),
      SeriesModel.getSeriesStats(),
      StreamModel.getStreamCountByType()
    ])

    const dashboardData = {
      overview: {
        total_streams: streamStats.total_streams,
        live_channels: streamStats.live_channels,
        movies: streamStats.movies,
        series: streamStats.series,
        unknown: streamStats.unknown
      },
      series: {
        total_series: seriesStats.total_series,
        series_with_episodes: seriesStats.series_with_episodes,
        series_without_episodes: seriesStats.series_without_episodes,
        total_episodes: seriesStats.total_episodes,
        orphan_episodes: seriesStats.orphan_episodes
      },
      content_distribution: contentDistribution,
      last_updated: new Date().toISOString()
    }

    res.json(dashboardData)
  } catch (error) {
    logger.error('Error fetching dashboard stats:', error)
    res.status(500).json({
      error: 'Failed to fetch dashboard statistics',
      message: error.message
    })
  }
})

// Get top categories
router.get('/categories', async (req, res) => {
  try {
    const sql = `
      SELECT 
        sc.category_name,
        COUNT(s.id) as count,
        sc.category_type
      FROM streams_categories sc
      LEFT JOIN streams s ON FIND_IN_SET(sc.id, s.category_id)
      GROUP BY sc.id, sc.category_name, sc.category_type
      HAVING count > 0
      ORDER BY count DESC
      LIMIT 10
    `
    
    const categories = await StreamModel.query(sql)
    res.json(categories)
  } catch (error) {
    logger.error('Error fetching categories:', error)
    res.status(500).json({
      error: 'Failed to fetch categories',
      message: error.message
    })
  }
})

// Get recent activity (placeholder for now)
router.get('/activity', async (req, res) => {
  try {
    // This would typically show recent additions, modifications, etc.
    const recentActivity = [
      {
        id: 1,
        type: 'movie_added',
        title: 'New movie added to database',
        timestamp: new Date().toISOString(),
        details: 'Movie: Sample Movie (2023)'
      },
      {
        id: 2,
        type: 'series_updated',
        title: 'Series episodes updated',
        timestamp: new Date(Date.now() - 3600000).toISOString(),
        details: 'Series: Sample Series - 5 new episodes'
      }
    ]

    res.json(recentActivity)
  } catch (error) {
    logger.error('Error fetching activity:', error)
    res.status(500).json({
      error: 'Failed to fetch activity',
      message: error.message
    })
  }
})

// Get system health
router.get('/health', async (req, res) => {
  try {
    const dbHealth = await StreamModel.healthCheck()
    
    const health = {
      database: dbHealth ? 'healthy' : 'unhealthy',
      server: 'healthy',
      memory_usage: process.memoryUsage(),
      uptime: process.uptime(),
      timestamp: new Date().toISOString()
    }

    res.json(health)
  } catch (error) {
    logger.error('Error checking system health:', error)
    res.status(500).json({
      error: 'Failed to check system health',
      message: error.message
    })
  }
})

export default router
