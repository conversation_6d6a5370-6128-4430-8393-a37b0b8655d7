import React from 'react'

interface ActivityItem {
  id: number
  type: string
  title: string
  timestamp: string
  details: string
}

interface RecentActivityProps {
  data: ActivityItem[]
}

const getActivityIcon = (type: string) => {
  switch (type) {
    case 'movie_added':
      return '🎬'
    case 'series_updated':
      return '📺'
    case 'cleanup_completed':
      return '🧹'
    case 'm3u_imported':
      return '📄'
    default:
      return '📝'
  }
}

const getActivityColor = (type: string) => {
  switch (type) {
    case 'movie_added':
      return 'text-blue-400'
    case 'series_updated':
      return 'text-green-400'
    case 'cleanup_completed':
      return 'text-yellow-400'
    case 'm3u_imported':
      return 'text-purple-400'
    default:
      return 'text-gray-400'
  }
}

const RecentActivity: React.FC<RecentActivityProps> = ({ data }) => {
  return (
    <div className="card">
      <div className="card-header">
        <h3 className="text-lg font-semibold text-vscode-text">
          Recent Activity
        </h3>
      </div>
      <div className="card-body">
        {data && data.length > 0 ? (
          <div className="space-y-4">
            {data.map((item) => (
              <div key={item.id} className="flex items-start space-x-3">
                <div className={`text-lg ${getActivityColor(item.type)}`}>
                  {getActivityIcon(item.type)}
                </div>
                <div className="flex-1 min-w-0">
                  <p className="text-sm font-medium text-vscode-text">
                    {item.title}
                  </p>
                  <p className="text-xs text-vscode-text-muted mt-1">
                    {item.details}
                  </p>
                  <p className="text-xs text-vscode-text-muted mt-1">
                    {new Date(item.timestamp).toLocaleString()}
                  </p>
                </div>
              </div>
            ))}
          </div>
        ) : (
          <div className="text-center py-8 text-vscode-text-muted">
            <div className="text-4xl mb-2">📝</div>
            <p>No recent activity</p>
          </div>
        )}
      </div>
    </div>
  )
}

export default RecentActivity
