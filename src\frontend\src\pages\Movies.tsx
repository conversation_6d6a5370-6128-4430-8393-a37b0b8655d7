import React, { useState } from 'react'
import { useQuery } from '@tanstack/react-query'
import { streamsApi } from '../services/api'

const Movies: React.FC = () => {
  const [showDuplicates, setShowDuplicates] = useState(false)

  const { data: movies, isLoading: moviesLoading } = useQuery({
    queryKey: ['movies'],
    queryFn: streamsApi.getMovies,
  })

  const { data: duplicates, isLoading: duplicatesLoading } = useQuery({
    queryKey: ['duplicate-movies'],
    queryFn: streamsApi.getDuplicateMovies,
    enabled: showDuplicates,
  })

  return (
    <div className="p-6">
      <div className="flex items-center justify-between mb-6">
        <div>
          <h1 className="text-2xl font-bold text-vscode-text">Movies</h1>
          <p className="text-vscode-text-muted">
            Manage your movie collection and find duplicates
          </p>
        </div>
        <div className="flex space-x-3">
          <button
            onClick={() => setShowDuplicates(!showDuplicates)}
            className={`btn-${showDuplicates ? 'primary' : 'secondary'}`}
          >
            {showDuplicates ? 'Show All Movies' : 'Find Duplicates'}
          </button>
          <button className="btn-primary">
            Smart Cleanup
          </button>
        </div>
      </div>

      {showDuplicates ? (
        <div className="space-y-4">
          <h2 className="text-xl font-semibold text-vscode-text">Duplicate Movies</h2>
          {duplicatesLoading ? (
            <div className="flex items-center justify-center py-12">
              <div className="spinner"></div>
              <span className="ml-2 text-vscode-text-muted">Finding duplicates...</span>
            </div>
          ) : (
            <div className="card">
              <div className="card-body">
                {duplicates?.data && duplicates.data.length > 0 ? (
                  <div className="space-y-4">
                    {duplicates.data.map((group: any, index: number) => (
                      <div key={group.id || index} className="border border-vscode-border rounded-sm p-4">
                        <h3 className="font-semibold text-vscode-text mb-2">
                          {group.title}
                        </h3>
                        <div className="space-y-2">
                          {group.items.map((item: any) => (
                            <div key={item.id} className="flex items-center justify-between bg-vscode-bg p-2 rounded-sm">
                              <div>
                                <span className="text-vscode-text">{item.stream_display_name}</span>
                                <div className="text-xs text-vscode-text-muted">
                                  {item.direct_source ? 'Direct Source' : 'Symlink'} • 
                                  Quality: {item.quality || 'Unknown'}
                                </div>
                              </div>
                              <button className="btn-danger text-xs">
                                Delete
                              </button>
                            </div>
                          ))}
                        </div>
                      </div>
                    ))}
                  </div>
                ) : (
                  <div className="text-center py-12">
                    <div className="text-4xl mb-4">🎉</div>
                    <h3 className="text-lg font-semibold text-vscode-text mb-2">
                      No Duplicates Found
                    </h3>
                    <p className="text-vscode-text-muted">
                      Your movie collection is clean!
                    </p>
                  </div>
                )}
              </div>
            </div>
          )}
        </div>
      ) : (
        <div className="card">
          <div className="card-header">
            <h2 className="text-lg font-semibold text-vscode-text">All Movies</h2>
          </div>
          <div className="card-body">
            {moviesLoading ? (
              <div className="flex items-center justify-center py-12">
                <div className="spinner"></div>
                <span className="ml-2 text-vscode-text-muted">Loading movies...</span>
              </div>
            ) : (
              <div className="overflow-x-auto">
                <table className="table">
                  <thead>
                    <tr>
                      <th>Title</th>
                      <th>Year</th>
                      <th>Rating</th>
                      <th>Type</th>
                      <th>Actions</th>
                    </tr>
                  </thead>
                  <tbody>
                    {movies?.data?.slice(0, 50).map((movie: any) => (
                      <tr key={movie.id}>
                        <td className="font-medium">{movie.stream_display_name}</td>
                        <td>{movie.year || 'N/A'}</td>
                        <td>{movie.rating ? `${movie.rating}/10` : 'N/A'}</td>
                        <td>
                          <span className={`px-2 py-1 rounded-sm text-xs ${
                            movie.direct_source ? 'bg-blue-500/20 text-blue-400' : 'bg-green-500/20 text-green-400'
                          }`}>
                            {movie.direct_source ? 'Direct' : 'Symlink'}
                          </span>
                        </td>
                        <td>
                          <button className="btn-danger text-xs">
                            Delete
                          </button>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
                {movies?.data?.length > 50 && (
                  <div className="mt-4 text-center">
                    <button className="btn-secondary">
                      Load More ({movies.data.length - 50} remaining)
                    </button>
                  </div>
                )}
              </div>
            )}
          </div>
        </div>
      )}
    </div>
  )
}

export default Movies
