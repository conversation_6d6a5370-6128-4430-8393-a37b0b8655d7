import React from 'react'
import { useQuery } from '@tanstack/react-query'
import { api } from '../../services/api'

const Header: React.FC = () => {
  const { data: health } = useQuery({
    queryKey: ['health'],
    queryFn: () => api.get('/api/health'),
    refetchInterval: 30000, // Check every 30 seconds
  })

  return (
    <header className="h-12 bg-vscode-panel border-b border-vscode-border flex items-center justify-between px-4">
      <div className="flex items-center space-x-4">
        <div className="flex items-center space-x-2">
          <div className="w-6 h-6 bg-gradient-to-br from-blue-500 to-purple-600 rounded-sm flex items-center justify-center">
            <span className="text-white text-xs font-bold">X</span>
          </div>
          <h1 className="text-lg font-semibold text-vscode-text">XUI Multimedia Manager</h1>
        </div>
      </div>

      <div className="flex items-center space-x-4">
        {/* Connection Status */}
        <div className="flex items-center space-x-2">
          <div className={`w-2 h-2 rounded-full ${
            health?.data?.database === 'connected' ? 'bg-vscode-success' : 'bg-vscode-error'
          }`} />
          <span className="text-sm text-vscode-text-muted">
            {health?.data?.database === 'connected' ? 'Connected' : 'Disconnected'}
          </span>
        </div>

        {/* Version */}
        <div className="text-sm text-vscode-text-muted">
          v{health?.data?.version || '1.0.0'}
        </div>

        {/* Menu Button */}
        <button className="p-2 hover:bg-vscode-border rounded-sm transition-colors">
          <svg className="w-4 h-4 text-vscode-text" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 5v.01M12 12v.01M12 19v.01M12 6a1 1 0 110-2 1 1 0 010 2zm0 7a1 1 0 110-2 1 1 0 010 2zm0 7a1 1 0 110-2 1 1 0 010 2z" />
          </svg>
        </button>
      </div>
    </header>
  )
}

export default Header
