import React from 'react'
import { useQuery } from '@tanstack/react-query'
import { api } from '../../services/api'

const Header: React.FC = () => {
  const { data: health } = useQuery({
    queryKey: ['health'],
    queryFn: () => api.get('/api/health'),
    refetchInterval: 30000, // Check every 30 seconds
  })

  return (
    <header className="h-8 bg-vscode-panel border-b border-vscode-border flex items-center justify-between px-3 text-xs">
      <div className="flex items-center space-x-3">
        <div className="flex items-center space-x-2">
          <div className="w-4 h-4 bg-gradient-to-br from-blue-500 to-purple-600 rounded-sm flex items-center justify-center">
            <span className="text-white text-xs font-bold">X</span>
          </div>
          <h1 className="text-sm font-medium text-vscode-text">XUI Multimedia Manager</h1>
        </div>
      </div>

      <div className="flex items-center space-x-3">
        {/* Connection Status */}
        <div className="flex items-center space-x-1">
          <div className={`w-1.5 h-1.5 rounded-full ${
            health?.data?.database === 'connected' ? 'bg-vscode-success' : 'bg-vscode-error'
          }`} />
          <span className="text-xs text-vscode-text-muted">
            {health?.data?.database === 'connected' ? 'Connected' : 'Disconnected'}
          </span>
        </div>

        {/* Version */}
        <div className="text-xs text-vscode-text-muted">
          v{health?.data?.version || '1.0.0'}
        </div>

        {/* Menu Button */}
        <button className="p-1 hover:bg-vscode-border rounded-sm transition-colors">
          <svg className="w-3 h-3 text-vscode-text" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 5v.01M12 12v.01M12 19v.01M12 6a1 1 0 110-2 1 1 0 010 2zm0 7a1 1 0 110-2 1 1 0 010 2zm0 7a1 1 0 110-2 1 1 0 010 2z" />
          </svg>
        </button>
      </div>
    </header>
  )
}

export default Header
