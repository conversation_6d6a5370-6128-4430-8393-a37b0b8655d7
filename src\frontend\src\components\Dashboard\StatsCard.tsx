import React from 'react'

interface StatsCardProps {
  title: string
  value: number
  icon: string
  color: 'blue' | 'cyan' | 'purple' | 'green' | 'yellow' | 'red'
}

const colorClasses = {
  blue: 'border-blue-500 bg-blue-500/10',
  cyan: 'border-cyan-500 bg-cyan-500/10',
  purple: 'border-purple-500 bg-purple-500/10',
  green: 'border-green-500 bg-green-500/10',
  yellow: 'border-yellow-500 bg-yellow-500/10',
  red: 'border-red-500 bg-red-500/10',
}

const StatsCard: React.FC<StatsCardProps> = ({ title, value, icon, color }) => {
  return (
    <div className={`card ${colorClasses[color]} border-l-4`}>
      <div className="card-body">
        <div className="flex items-center justify-between">
          <div>
            <p className="text-sm text-vscode-text-muted mb-1">{title}</p>
            <p className="text-2xl font-bold text-vscode-text">
              {value.toLocaleString()}
            </p>
          </div>
          <div className="text-3xl opacity-60">
            {icon}
          </div>
        </div>
      </div>
    </div>
  )
}

export default StatsCard
