import { Router } from 'express'
import multer from 'multer'
import { M3UParser } from '../services/M3UParser.js'
import logger from '../utils/logger.js'

const router = Router()

// Configure multer for file uploads
const upload = multer({
  storage: multer.memoryStorage(),
  limits: {
    fileSize: 50 * 1024 * 1024, // 50MB limit
  },
  fileFilter: (req, file, cb) => {
    if (file.mimetype === 'text/plain' || file.originalname.endsWith('.m3u') || file.originalname.endsWith('.m3u8')) {
      cb(null, true)
    } else {
      cb(new Error('Only M3U files are allowed'))
    }
  }
})

// Parse M3U from uploaded file
router.post('/parse', upload.single('m3u_file'), async (req, res) => {
  try {
    if (!req.file) {
      return res.status(400).json({
        error: 'No file uploaded',
        message: 'Please upload an M3U file'
      })
    }

    const content = req.file.buffer.toString('utf-8')
    
    // Validate M3U content
    const validation = M3UParser.validateM3U(content)
    if (!validation.isValid) {
      return res.status(400).json({
        error: 'Invalid M3U file',
        message: 'The uploaded file is not a valid M3U playlist',
        details: validation.errors
      })
    }

    // Parse the M3U content
    const entries = M3UParser.parseM3U(content)
    const classified = M3UParser.classifyEntries(entries)
    
    // Group series entries
    const seriesGroups = M3UParser.groupSeriesEntries(classified.series)

    res.json({
      message: 'M3U file parsed successfully',
      statistics: {
        total_entries: entries.length,
        movies: classified.movies.length,
        series: classified.series.length,
        live_channels: classified.live.length,
        series_groups: seriesGroups.size
      },
      data: {
        movies: classified.movies,
        series: classified.series,
        live_channels: classified.live,
        series_groups: Array.from(seriesGroups.entries()).map(([name, episodes]) => ({
          series_name: name,
          episode_count: episodes.length,
          episodes: episodes
        }))
      }
    })
  } catch (error) {
    logger.error('Error parsing M3U file:', error)
    res.status(500).json({
      error: 'Failed to parse M3U file',
      message: error.message
    })
  }
})

// Parse M3U from URL
router.post('/parse-url', async (req, res) => {
  try {
    const { url } = req.body
    
    if (!url) {
      return res.status(400).json({
        error: 'URL required',
        message: 'Please provide a URL to the M3U playlist'
      })
    }

    // Fetch M3U content from URL
    const response = await fetch(url)
    if (!response.ok) {
      return res.status(400).json({
        error: 'Failed to fetch M3U',
        message: `HTTP ${response.status}: ${response.statusText}`
      })
    }

    const content = await response.text()
    
    // Validate M3U content
    const validation = M3UParser.validateM3U(content)
    if (!validation.isValid) {
      return res.status(400).json({
        error: 'Invalid M3U content',
        message: 'The URL does not contain a valid M3U playlist',
        details: validation.errors
      })
    }

    // Parse the M3U content
    const entries = M3UParser.parseM3U(content)
    const classified = M3UParser.classifyEntries(entries)
    
    // Group series entries
    const seriesGroups = M3UParser.groupSeriesEntries(classified.series)

    res.json({
      message: 'M3U URL parsed successfully',
      source_url: url,
      statistics: {
        total_entries: entries.length,
        movies: classified.movies.length,
        series: classified.series.length,
        live_channels: classified.live.length,
        series_groups: seriesGroups.size
      },
      data: {
        movies: classified.movies,
        series: classified.series,
        live_channels: classified.live,
        series_groups: Array.from(seriesGroups.entries()).map(([name, episodes]) => ({
          series_name: name,
          episode_count: episodes.length,
          episodes: episodes
        }))
      }
    })
  } catch (error) {
    logger.error('Error parsing M3U URL:', error)
    res.status(500).json({
      error: 'Failed to parse M3U URL',
      message: error.message
    })
  }
})

// Validate M3U content
router.post('/validate', async (req, res) => {
  try {
    const { content } = req.body
    
    if (!content) {
      return res.status(400).json({
        error: 'Content required',
        message: 'Please provide M3U content to validate'
      })
    }

    const validation = M3UParser.validateM3U(content)
    
    res.json({
      is_valid: validation.isValid,
      errors: validation.errors,
      message: validation.isValid ? 'M3U content is valid' : 'M3U content has validation errors'
    })
  } catch (error) {
    logger.error('Error validating M3U content:', error)
    res.status(500).json({
      error: 'Failed to validate M3U content',
      message: error.message
    })
  }
})

export default router
