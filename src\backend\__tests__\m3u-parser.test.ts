import { describe, it, expect } from 'vitest'
import { M3UParser } from '../services/M3UParser.js'

describe('M3U Parser', () => {
  const sampleM3U = `#EXTM3U
#EXTINF:-1 tvg-id="movie1" tvg-name="Test Movie (2023)" tvg-logo="http://example.com/logo.png" group-title="Movies",Test Movie (2023)
http://example.com/movie/test-movie-2023.mp4
#EXTINF:-1 tvg-id="series1" tvg-name="Test Series S01E01" group-title="Series",Test Series S01E01
http://example.com/series/test-series-s01e01.mp4
#EXTINF:-1 tvg-id="live1" tvg-name="Test Channel HD" group-title="Live TV",Test Channel HD
http://example.com/live/test-channel.m3u8`

  it('should validate M3U content', () => {
    const validation = M3UParser.validateM3U(sampleM3U)
    expect(validation.isValid).toBe(true)
    expect(validation.errors).toHaveLength(0)
  })

  it('should parse M3U entries correctly', () => {
    const entries = M3UParser.parseM3U(sampleM3U)
    expect(entries).toHaveLength(3)
    
    // Check movie entry
    const movie = entries.find(e => e.title.includes('Test Movie'))
    expect(movie).toBeDefined()
    expect(movie?.type).toBe('movie')
    expect(movie?.year).toBe(2023)
    
    // Check series entry
    const series = entries.find(e => e.title.includes('Test Series'))
    expect(series).toBeDefined()
    expect(series?.type).toBe('series')
    expect(series?.season).toBe(1)
    expect(series?.episode).toBe(1)
    
    // Check live entry
    const live = entries.find(e => e.title.includes('Test Channel'))
    expect(live).toBeDefined()
    expect(live?.type).toBe('live')
  })

  it('should classify entries by type', () => {
    const entries = M3UParser.parseM3U(sampleM3U)
    const classified = M3UParser.classifyEntries(entries)
    
    expect(classified.movies).toHaveLength(1)
    expect(classified.series).toHaveLength(1)
    expect(classified.live).toHaveLength(1)
  })

  it('should group series entries', () => {
    const seriesM3U = `#EXTM3U
#EXTINF:-1,Breaking Bad S01E01
http://example.com/breaking-bad-s01e01.mp4
#EXTINF:-1,Breaking Bad S01E02
http://example.com/breaking-bad-s01e02.mp4
#EXTINF:-1,Game of Thrones S01E01
http://example.com/got-s01e01.mp4`

    const entries = M3UParser.parseM3U(seriesM3U)
    const seriesEntries = entries.filter(e => e.type === 'series')
    const grouped = M3UParser.groupSeriesEntries(seriesEntries)
    
    expect(grouped.size).toBe(2)
    expect(grouped.has('Breaking Bad')).toBe(true)
    expect(grouped.has('Game of Thrones')).toBe(true)
    expect(grouped.get('Breaking Bad')).toHaveLength(2)
  })

  it('should handle invalid M3U content', () => {
    const invalidM3U = 'This is not a valid M3U file'
    const validation = M3UParser.validateM3U(invalidM3U)
    
    expect(validation.isValid).toBe(false)
    expect(validation.errors.length).toBeGreaterThan(0)
  })
})
