import axios from 'axios'

// Create axios instance with default config
export const api = axios.create({
  baseURL: import.meta.env.VITE_API_URL || 'http://localhost:3001',
  timeout: 30000,
  headers: {
    'Content-Type': 'application/json',
  },
})

// Request interceptor
api.interceptors.request.use(
  (config) => {
    // Add auth token if available
    const token = localStorage.getItem('auth_token')
    if (token) {
      config.headers.Authorization = `Bearer ${token}`
    }
    return config
  },
  (error) => {
    return Promise.reject(error)
  }
)

// Response interceptor
api.interceptors.response.use(
  (response) => {
    return response
  },
  (error) => {
    // Handle common errors
    if (error.response?.status === 401) {
      // Unauthorized - clear token and redirect to login
      localStorage.removeItem('auth_token')
      // window.location.href = '/login'
    }
    
    return Promise.reject(error)
  }
)

// API service functions
export const dashboardApi = {
  getStats: () => api.get('/api/dashboard/stats'),
  getCategories: () => api.get('/api/dashboard/categories'),
  getActivity: () => api.get('/api/dashboard/activity'),
  getHealth: () => api.get('/api/dashboard/health'),
}

export const streamsApi = {
  getAll: (params?: { type?: number; limit?: number; offset?: number }) => 
    api.get('/api/streams', { params }),
  getMovies: () => api.get('/api/streams/movies'),
  getDuplicateMovies: () => api.get('/api/streams/movies/duplicates'),
  getTypes: () => api.get('/api/streams/types'),
  deleteStream: (id: number) => api.delete(`/api/streams/${id}`),
  bulkDelete: (streamIds: number[]) => 
    api.post('/api/streams/bulk-delete', { stream_ids: streamIds }),
}

export const seriesApi = {
  getAll: () => api.get('/api/series'),
  getById: (id: number) => api.get(`/api/series/${id}`),
  getWithoutEpisodes: () => api.get('/api/series/orphaned/without-episodes'),
  getOrphanEpisodes: () => api.get('/api/series/orphaned/episodes'),
  getDuplicateEpisodes: () => api.get('/api/series/duplicates/episodes'),
  deleteSeries: (id: number) => api.delete(`/api/series/${id}`),
  deleteOrphanEpisodes: () => api.delete('/api/series/orphaned/episodes'),
  bulkDelete: (seriesIds: number[]) => 
    api.post('/api/series/bulk-delete', { series_ids: seriesIds }),
}

export const m3uApi = {
  parseFile: (file: File) => {
    const formData = new FormData()
    formData.append('m3u_file', file)
    return api.post('/api/m3u/parse', formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    })
  },
  parseUrl: (url: string) => 
    api.post('/api/m3u/parse-url', { url }),
  validate: (content: string) => 
    api.post('/api/m3u/validate', { content }),
}

export default api
