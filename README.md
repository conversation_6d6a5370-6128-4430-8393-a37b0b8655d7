# XUI Multimedia Manager

An ultra-powerful multimedia file manager with a Visual Studio Code-like interface, designed to bring elegance to chaos and restore order to your streaming kingdom.

## 🚀 Features

### 🔥 Movie Manager
- Detect duplicate movies with the same quality
- Smart deletion modes with protection for 4K versions and symlinks
- Prioritize deleting duplicate direct_source files
- Confirmation dialogs for all operations

### 🌟 Series Manager
- Read all series information: name, seasons, episodes, TMDB ID
- Detect duplicate episodes and prioritize symlinks
- Keep only one file per episode and season
- Clean orphan episodes and series without episodes

### 🧩 Database Integration
- Full XUI (Xtream UI) database support
- MySQL/MariaDB connection with connection pooling
- Real-time statistics and health monitoring
- Advanced orphan detection and cleanup

### 🗺️ M3U Manager
- Parse M3U playlists and automatically classify content
- Distinguish between movies, series, and live channels
- Extract metadata including season/episode information
- Support for file upload and URL parsing

### 🕸️ Modern Interface
- Visual Studio Code-inspired dark theme
- Real-time dashboard with charts and statistics
- Responsive design for desktop and web
- WebSocket support for live updates

## 🛠️ Technology Stack

- **Frontend**: React 18 + TypeScript + Vite + Tailwind CSS
- **Backend**: Node.js + Express + TypeScript
- **Database**: MySQL/MariaDB
- **Desktop**: Electron (portable .exe)
- **Real-time**: Socket.IO
- **Charts**: Custom SVG-based charts
- **API**: RESTful API with comprehensive endpoints

## 📦 Installation

### Prerequisites
- Node.js 18+ 
- MySQL/MariaDB database
- Git

### Quick Start

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd xui-multimedia-manager
   ```

2. **Install dependencies**
   ```bash
   npm install
   ```

3. **Configure environment**
   ```bash
   cp .env.example .env
   # Edit .env with your database credentials
   ```

4. **Set up database**
   - Create a MySQL/MariaDB database
   - Import your XUI database schema
   - Update connection details in `.env`

5. **Start development servers**
   ```bash
   npm run dev
   ```
   This starts both frontend (http://localhost:5173) and backend (http://localhost:3001)

### Production Build

1. **Build the application**
   ```bash
   npm run build
   ```

2. **Start production server**
   ```bash
   npm start
   ```

### Desktop Application

1. **Build desktop app**
   ```bash
   npm run build:desktop
   ```

2. **Build portable executable**
   ```bash
   npm run build:portable
   ```

The portable .exe will be created in the `release` folder.

## 🔧 Configuration

### Database Configuration
Update `.env` file with your database settings:

```env
DB_HOST=localhost
DB_PORT=3306
DB_USER=root
DB_PASSWORD=your_password
DB_NAME=xui_database
```

### TMDB Integration
Add your TMDB API key:

```env
TMDB_API_KEY=your_tmdb_api_key_here
```

### File System
Configure media paths:

```env
MEDIA_ROOT_PATH=/path/to/your/media/files
TEMP_UPLOAD_PATH=./temp/uploads
MAX_FILE_SIZE=**********
```

## 📊 Database Schema

The application works with XUI (Xtream UI) database schema including:

- `streams` - Main content table
- `streams_types` - Content type definitions
- `streams_series` - Series information
- `streams_episodes` - Episode details
- `streams_categories` - Content categories

See `tables.txt` for complete schema details.

## 🎯 Usage

### Dashboard
- View comprehensive statistics
- Monitor system health
- Browse content distribution charts
- Check recent activity

### Movie Management
- Browse all movies
- Find and manage duplicates
- Smart cleanup with protection rules
- Bulk operations

### Series Management
- Organize series and episodes
- Find orphaned content
- Detect duplicate episodes
- Clean incomplete series

### M3U Management
- Upload M3U files or parse from URLs
- Automatic content classification
- Preview parsed content
- Import to database

## 🔒 Security Features

- Helmet.js for security headers
- CORS protection
- Input validation with Joi
- SQL injection prevention
- File upload restrictions

## 🚀 Performance

- Connection pooling for database
- Efficient queries with pagination
- Real-time updates via WebSocket
- Optimized frontend with code splitting
- Caching strategies

## 🧪 Testing

```bash
# Run tests
npm test

# Run tests with UI
npm run test:ui
```

## 📝 API Documentation

### Endpoints

- `GET /api/dashboard/stats` - Dashboard statistics
- `GET /api/streams` - List all streams
- `GET /api/streams/movies` - Get movies
- `GET /api/streams/movies/duplicates` - Find duplicate movies
- `GET /api/series` - List all series
- `GET /api/series/orphaned/episodes` - Get orphan episodes
- `POST /api/m3u/parse` - Parse M3U file
- `POST /api/m3u/parse-url` - Parse M3U from URL

### WebSocket Events

- `cleanup-progress` - Cleanup operation updates
- `scan-progress` - Library scan updates
- `stats-update` - Real-time statistics

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## 📄 License

MIT License - see LICENSE file for details

## 🆘 Support

For issues and questions:
1. Check the documentation
2. Search existing issues
3. Create a new issue with detailed information

## 🎉 Acknowledgments

- Inspired by Visual Studio Code's interface design
- Built for the XUI/Xtream UI community
- TMDB for movie/series metadata

---

**"We don't want just another file cleaner. We want a digital butler wielding a samurai sword, who knows exactly what to keep, what to cut, and what to protect."**
