import React, { useState } from 'react'
import { useMutation } from '@tanstack/react-query'
import { m3uApi } from '../services/api'

const M3UManager: React.FC = () => {
  const [activeTab, setActiveTab] = useState<'upload' | 'url' | 'results'>('upload')
  const [parseResults, setParseResults] = useState<any>(null)
  const [dragOver, setDragOver] = useState(false)

  const parseFileMutation = useMutation({
    mutationFn: m3uApi.parseFile,
    onSuccess: (data) => {
      setParseResults(data.data)
      setActiveTab('results')
    },
  })

  const parseUrlMutation = useMutation({
    mutationFn: m3uApi.parseUrl,
    onSuccess: (data) => {
      setParseResults(data.data)
      setActiveTab('results')
    },
  })

  const handleFileUpload = (file: File) => {
    if (file && (file.name.endsWith('.m3u') || file.name.endsWith('.m3u8'))) {
      parseFileMutation.mutate(file)
    } else {
      alert('Please select a valid M3U file')
    }
  }

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault()
    setDragOver(false)
    const files = Array.from(e.dataTransfer.files)
    if (files.length > 0) {
      handleFileUpload(files[0])
    }
  }

  const handleUrlSubmit = (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault()
    const formData = new FormData(e.currentTarget)
    const url = formData.get('url') as string
    if (url) {
      parseUrlMutation.mutate(url)
    }
  }

  const tabs = [
    { id: 'upload', label: 'Upload File' },
    { id: 'url', label: 'From URL' },
    { id: 'results', label: 'Results', disabled: !parseResults },
  ]

  return (
    <div className="p-6">
      <div className="flex items-center justify-between mb-6">
        <div>
          <h1 className="text-2xl font-bold text-vscode-text">M3U Manager</h1>
          <p className="text-vscode-text-muted">
            Parse and analyze M3U playlists to organize your content
          </p>
        </div>
      </div>

      {/* Tabs */}
      <div className="flex space-x-1 mb-6">
        {tabs.map((tab) => (
          <button
            key={tab.id}
            onClick={() => !tab.disabled && setActiveTab(tab.id as any)}
            disabled={tab.disabled}
            className={`px-4 py-2 rounded-sm font-medium transition-colors ${
              activeTab === tab.id
                ? 'bg-vscode-accent text-white'
                : tab.disabled
                ? 'bg-vscode-panel text-vscode-text-muted cursor-not-allowed'
                : 'bg-vscode-panel text-vscode-text hover:bg-vscode-border'
            }`}
          >
            {tab.label}
          </button>
        ))}
      </div>

      {/* Content */}
      <div className="card">
        <div className="card-body">
          {activeTab === 'upload' && (
            <div className="space-y-6">
              <div
                className={`border-2 border-dashed rounded-lg p-12 text-center transition-colors ${
                  dragOver
                    ? 'border-vscode-accent bg-vscode-accent/10'
                    : 'border-vscode-border hover:border-vscode-accent/50'
                }`}
                onDragOver={(e) => {
                  e.preventDefault()
                  setDragOver(true)
                }}
                onDragLeave={() => setDragOver(false)}
                onDrop={handleDrop}
              >
                <div className="text-4xl mb-4">📄</div>
                <h3 className="text-lg font-semibold text-vscode-text mb-2">
                  Drop your M3U file here
                </h3>
                <p className="text-vscode-text-muted mb-4">
                  or click to browse and select a file
                </p>
                <input
                  type="file"
                  accept=".m3u,.m3u8"
                  onChange={(e) => {
                    const file = e.target.files?.[0]
                    if (file) handleFileUpload(file)
                  }}
                  className="hidden"
                  id="file-upload"
                />
                <label htmlFor="file-upload" className="btn-primary cursor-pointer">
                  Choose File
                </label>
              </div>

              {parseFileMutation.isPending && (
                <div className="flex items-center justify-center py-8">
                  <div className="spinner"></div>
                  <span className="ml-2 text-vscode-text-muted">Parsing M3U file...</span>
                </div>
              )}

              {parseFileMutation.error && (
                <div className="bg-vscode-error/20 border border-vscode-error rounded-sm p-4">
                  <h4 className="font-semibold text-vscode-error mb-2">Error</h4>
                  <p className="text-vscode-text">
                    {(parseFileMutation.error as any)?.response?.data?.message || 'Failed to parse M3U file'}
                  </p>
                </div>
              )}
            </div>
          )}

          {activeTab === 'url' && (
            <div className="space-y-6">
              <form onSubmit={handleUrlSubmit} className="space-y-4">
                <div>
                  <label htmlFor="url" className="block text-sm font-medium text-vscode-text mb-2">
                    M3U Playlist URL
                  </label>
                  <input
                    type="url"
                    id="url"
                    name="url"
                    placeholder="https://example.com/playlist.m3u"
                    className="input-primary w-full"
                    required
                  />
                </div>
                <button
                  type="submit"
                  disabled={parseUrlMutation.isPending}
                  className="btn-primary"
                >
                  {parseUrlMutation.isPending ? 'Parsing...' : 'Parse URL'}
                </button>
              </form>

              {parseUrlMutation.isPending && (
                <div className="flex items-center justify-center py-8">
                  <div className="spinner"></div>
                  <span className="ml-2 text-vscode-text-muted">Fetching and parsing M3U...</span>
                </div>
              )}

              {parseUrlMutation.error && (
                <div className="bg-vscode-error/20 border border-vscode-error rounded-sm p-4">
                  <h4 className="font-semibold text-vscode-error mb-2">Error</h4>
                  <p className="text-vscode-text">
                    {(parseUrlMutation.error as any)?.response?.data?.message || 'Failed to parse M3U URL'}
                  </p>
                </div>
              )}
            </div>
          )}

          {activeTab === 'results' && parseResults && (
            <div className="space-y-6">
              {/* Statistics */}
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                <div className="bg-vscode-bg p-4 rounded-sm border border-vscode-border">
                  <div className="text-2xl font-bold text-vscode-accent">
                    {parseResults.statistics.total_entries.toLocaleString()}
                  </div>
                  <div className="text-sm text-vscode-text-muted">Total Entries</div>
                </div>
                <div className="bg-vscode-bg p-4 rounded-sm border border-vscode-border">
                  <div className="text-2xl font-bold text-blue-400">
                    {parseResults.statistics.movies.toLocaleString()}
                  </div>
                  <div className="text-sm text-vscode-text-muted">Movies</div>
                </div>
                <div className="bg-vscode-bg p-4 rounded-sm border border-vscode-border">
                  <div className="text-2xl font-bold text-green-400">
                    {parseResults.statistics.series.toLocaleString()}
                  </div>
                  <div className="text-sm text-vscode-text-muted">Series</div>
                </div>
                <div className="bg-vscode-bg p-4 rounded-sm border border-vscode-border">
                  <div className="text-2xl font-bold text-cyan-400">
                    {parseResults.statistics.live_channels.toLocaleString()}
                  </div>
                  <div className="text-sm text-vscode-text-muted">Live Channels</div>
                </div>
              </div>

              {/* Content Preview */}
              <div className="space-y-4">
                <h3 className="text-lg font-semibold text-vscode-text">Content Preview</h3>
                
                {/* Movies */}
                {parseResults.data.movies.length > 0 && (
                  <div>
                    <h4 className="font-medium text-vscode-text mb-2">
                      Movies ({parseResults.data.movies.length})
                    </h4>
                    <div className="bg-vscode-bg rounded-sm border border-vscode-border max-h-48 overflow-y-auto">
                      {parseResults.data.movies.slice(0, 10).map((movie: any, index: number) => (
                        <div key={index} className="p-3 border-b border-vscode-border last:border-b-0">
                          <div className="font-medium text-vscode-text">{movie.title}</div>
                          <div className="text-xs text-vscode-text-muted">
                            {movie.year && `Year: ${movie.year} • `}
                            Group: {movie.group || 'None'}
                          </div>
                        </div>
                      ))}
                      {parseResults.data.movies.length > 10 && (
                        <div className="p-3 text-center text-vscode-text-muted">
                          ... and {parseResults.data.movies.length - 10} more
                        </div>
                      )}
                    </div>
                  </div>
                )}

                {/* Series */}
                {parseResults.data.series_groups.length > 0 && (
                  <div>
                    <h4 className="font-medium text-vscode-text mb-2">
                      Series ({parseResults.data.series_groups.length} series)
                    </h4>
                    <div className="bg-vscode-bg rounded-sm border border-vscode-border max-h-48 overflow-y-auto">
                      {parseResults.data.series_groups.slice(0, 10).map((series: any, index: number) => (
                        <div key={index} className="p-3 border-b border-vscode-border last:border-b-0">
                          <div className="font-medium text-vscode-text">{series.series_name}</div>
                          <div className="text-xs text-vscode-text-muted">
                            {series.episode_count} episodes
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>
                )}

                {/* Actions */}
                <div className="flex space-x-3 pt-4">
                  <button className="btn-primary">
                    Import to Database
                  </button>
                  <button className="btn-secondary">
                    Export Report
                  </button>
                  <button
                    onClick={() => {
                      setParseResults(null)
                      setActiveTab('upload')
                    }}
                    className="btn-secondary"
                  >
                    Parse Another
                  </button>
                </div>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  )
}

export default M3UManager
