import React from 'react'
import { useQuery } from '@tanstack/react-query'
import { dashboardApi } from '../services/api'
import StatsCard from '../components/Dashboard/StatsCard'
import ContentDistributionChart from '../components/Dashboard/ContentDistributionChart'
import TopCategoriesChart from '../components/Dashboard/TopCategoriesChart'
import RecentActivity from '../components/Dashboard/RecentActivity'

const Dashboard: React.FC = () => {
  const { data: stats, isLoading: statsLoading } = useQuery({
    queryKey: ['dashboard-stats'],
    queryFn: dashboardApi.getStats,
    refetchInterval: 30000, // Refresh every 30 seconds
  })

  const { data: categories } = useQuery({
    queryKey: ['dashboard-categories'],
    queryFn: dashboardApi.getCategories,
  })

  const { data: activity } = useQuery({
    queryKey: ['dashboard-activity'],
    queryFn: dashboardApi.getActivity,
  })

  if (statsLoading) {
    return (
      <div className="p-6 flex items-center justify-center h-full">
        <div className="spinner"></div>
        <span className="ml-2 text-vscode-text-muted">Loading dashboard...</span>
      </div>
    )
  }

  const dashboardData = stats?.data

  return (
    <div className="p-6 space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-vscode-text">Dashboard</h1>
          <p className="text-vscode-text-muted">
            {dashboardData?.overview.total_streams.toLocaleString()} total entries
          </p>
        </div>
        <div className="text-sm text-vscode-text-muted">
          Last updated: {new Date(dashboardData?.last_updated || Date.now()).toLocaleTimeString()}
        </div>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <StatsCard
          title="Total Streams"
          value={dashboardData?.overview.total_streams || 0}
          icon="📊"
          color="blue"
        />
        <StatsCard
          title="Live TV Channels"
          value={dashboardData?.overview.live_channels || 0}
          icon="📺"
          color="cyan"
        />
        <StatsCard
          title="Movies"
          value={dashboardData?.overview.movies || 0}
          icon="🎬"
          color="purple"
        />
        <StatsCard
          title="Series"
          value={dashboardData?.overview.series || 0}
          icon="📺"
          color="green"
        />
      </div>

      {/* Charts Section */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Content Distribution Chart */}
        <div className="chart-container">
          <h3 className="text-lg font-semibold text-vscode-text mb-4">
            Content Distribution by Type
          </h3>
          <ContentDistributionChart data={dashboardData?.content_distribution || []} />
        </div>

        {/* Top Categories Chart */}
        <div className="chart-container">
          <h3 className="text-lg font-semibold text-vscode-text mb-4">
            Top 10 Categories
          </h3>
          <TopCategoriesChart data={categories?.data || []} />
        </div>
      </div>

      {/* Bottom Section */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Browse Content by Type */}
        <div className="lg:col-span-2">
          <div className="card">
            <div className="card-header">
              <h3 className="text-lg font-semibold text-vscode-text">
                Browse Content by Type
              </h3>
            </div>
            <div className="card-body">
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div className="text-center p-4 bg-vscode-bg rounded-sm border border-vscode-border hover:border-vscode-accent transition-colors cursor-pointer">
                  <div className="text-2xl mb-2">📺</div>
                  <div className="text-lg font-semibold text-vscode-text">
                    {dashboardData?.overview.live_channels || 0}
                  </div>
                  <div className="text-sm text-vscode-text-muted">Live TV</div>
                  <div className="text-xs text-vscode-text-muted mt-1">
                    in 91 categories
                  </div>
                </div>

                <div className="text-center p-4 bg-vscode-bg rounded-sm border border-vscode-border hover:border-vscode-accent transition-colors cursor-pointer">
                  <div className="text-2xl mb-2">🎬</div>
                  <div className="text-lg font-semibold text-vscode-text">
                    {dashboardData?.overview.movies || 0}
                  </div>
                  <div className="text-sm text-vscode-text-muted">Movies</div>
                  <div className="text-xs text-vscode-text-muted mt-1">
                    in 27 categories
                  </div>
                </div>

                <div className="text-center p-4 bg-vscode-bg rounded-sm border border-vscode-border hover:border-vscode-accent transition-colors cursor-pointer">
                  <div className="text-2xl mb-2">📺</div>
                  <div className="text-lg font-semibold text-vscode-text">
                    {dashboardData?.overview.series || 0}
                  </div>
                  <div className="text-sm text-vscode-text-muted">Series</div>
                  <div className="text-xs text-vscode-text-muted mt-1">
                    in 33 categories
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Recent Activity */}
        <div>
          <RecentActivity data={activity?.data || []} />
        </div>
      </div>

      {/* Series Statistics */}
      {dashboardData?.series && (
        <div className="card">
          <div className="card-header">
            <h3 className="text-lg font-semibold text-vscode-text">
              Series Statistics
            </h3>
          </div>
          <div className="card-body">
            <div className="grid grid-cols-2 md:grid-cols-5 gap-4">
              <div className="text-center">
                <div className="text-2xl font-bold text-vscode-accent">
                  {dashboardData.series.total_series}
                </div>
                <div className="text-sm text-vscode-text-muted">Total Series</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-vscode-success">
                  {dashboardData.series.series_with_episodes}
                </div>
                <div className="text-sm text-vscode-text-muted">With Episodes</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-vscode-warning">
                  {dashboardData.series.series_without_episodes}
                </div>
                <div className="text-sm text-vscode-text-muted">Without Episodes</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-vscode-info">
                  {dashboardData.series.total_episodes}
                </div>
                <div className="text-sm text-vscode-text-muted">Total Episodes</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-vscode-error">
                  {dashboardData.series.orphan_episodes}
                </div>
                <div className="text-sm text-vscode-text-muted">Orphan Episodes</div>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}

export default Dashboard
