import express from 'express'
import cors from 'cors'
import path from 'path'

const app = express()
const port = process.env.PORT || 3001

// Middleware
app.use(cors({
  origin: 'http://localhost:5173',
  credentials: true
}))
app.use(express.json())

// Simple logging
const log = {
  info: (...args) => console.log('[INFO]', ...args),
  error: (...args) => console.error('[ERROR]', ...args)
}

// Health check
app.get('/api/health', (req, res) => {
  res.json({
    status: 'ok',
    timestamp: new Date().toISOString(),
    database: 'disconnected',
    version: '1.0.0'
  })
})

// Dashboard stats
app.get('/api/dashboard/stats', (req, res) => {
  res.json({
    overview: {
      total_streams: 1250,
      live_channels: 850,
      movies: 300,
      series: 100,
      unknown: 0
    },
    series: {
      total_series: 100,
      series_with_episodes: 85,
      series_without_episodes: 15,
      total_episodes: 2500,
      orphan_episodes: 25
    },
    content_distribution: [
      { type_name: 'Live TV', count: 850 },
      { type_name: 'Movies', count: 300 },
      { type_name: 'Series', count: 100 }
    ],
    last_updated: new Date().toISOString()
  })
})

// Categories
app.get('/api/dashboard/categories', (req, res) => {
  res.json([
    { category_name: 'Action Movies', count: 45, category_type: 'movie' },
    { category_name: 'Comedy Movies', count: 38, category_type: 'movie' },
    { category_name: 'Drama Series', count: 32, category_type: 'series' },
    { category_name: 'News Channels', count: 28, category_type: 'live' },
    { category_name: 'Sports Channels', count: 25, category_type: 'live' }
  ])
})

// Activity
app.get('/api/dashboard/activity', (req, res) => {
  res.json([
    {
      id: 1,
      type: 'movie_added',
      title: 'New movie added to database',
      timestamp: new Date().toISOString(),
      details: 'Movie: Sample Movie (2023)'
    },
    {
      id: 2,
      type: 'series_updated',
      title: 'Series episodes updated',
      timestamp: new Date(Date.now() - 3600000).toISOString(),
      details: 'Series: Sample Series - 5 new episodes'
    }
  ])
})

// Basic API endpoints
app.get('/api/streams', (req, res) => {
  res.json({ message: 'Streams API endpoint', data: [] })
})

app.get('/api/series', (req, res) => {
  res.json({ message: 'Series API endpoint', data: [] })
})

app.get('/api/m3u', (req, res) => {
  res.json({ message: 'M3U API endpoint' })
})

// Error handling
app.use((error, req, res, next) => {
  log.error('Unhandled error:', error)
  res.status(500).json({
    error: 'Internal Server Error',
    message: 'Something went wrong',
    timestamp: new Date().toISOString()
  })
})

// 404 handler
app.use((req, res) => {
  res.status(404).json({
    error: 'Not Found',
    message: `Route ${req.originalUrl} not found`,
    timestamp: new Date().toISOString()
  })
})

// Start server
app.listen(port, () => {
  log.info(`Server running on port ${port}`)
  log.info(`Environment: ${process.env.NODE_ENV || 'development'}`)
  log.info(`Frontend: http://localhost:5173`)
  log.info(`Backend: http://localhost:${port}`)
})

// Graceful shutdown
process.on('SIGTERM', () => {
  log.info('SIGTERM received, shutting down gracefully')
  process.exit(0)
})

process.on('SIGINT', () => {
  log.info('SIGINT received, shutting down gracefully')
  process.exit(0)
})
