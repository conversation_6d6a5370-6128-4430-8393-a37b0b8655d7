{"name": "xui-multimedia-manager", "version": "1.0.0", "description": "Ultra-powerful multimedia file manager with VS Code-like interface", "main": "dist/main.js", "type": "module", "scripts": {"setup": "node setup.js", "dev": "concurrently \"npm run dev:backend\" \"npm run dev:frontend\"", "dev:backend": "nodemon --exec node --loader ts-node/esm src/backend/server.ts", "dev:frontend": "vite", "build": "npm run build:backend && npm run build:frontend", "build:backend": "tsc -p tsconfig.backend.json", "build:frontend": "vite build", "build:desktop": "npm run build && electron-builder", "build:portable": "npm run build && electron-builder --portable", "preview": "vite preview", "start": "node dist/main.js", "electron": "electron .", "electron:dev": "concurrently \"npm run dev:backend\" \"wait-on http://localhost:5173 && electron .\"", "test": "vitest", "test:ui": "vitest --ui", "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "lint:fix": "eslint . --ext ts,tsx --fix"}, "keywords": ["multimedia", "file-manager", "xui", "tmdb", "m3u", "streaming"], "author": "XUI Multimedia Manager", "license": "MIT", "dependencies": {"@tanstack/react-query": "^5.17.0", "@tanstack/react-table": "^8.11.0", "axios": "^1.6.5", "express": "^4.18.2", "mysql2": "^3.6.5", "react": "^18.2.0", "react-dom": "^18.2.0", "react-router-dom": "^6.20.1", "socket.io": "^4.7.4", "socket.io-client": "^4.7.4", "cors": "^2.8.5", "helmet": "^7.1.0", "dotenv": "^16.3.1", "multer": "^1.4.5-lts.1", "bcryptjs": "^2.4.3", "jsonwebtoken": "^9.0.2", "winston": "^3.11.0", "joi": "^17.11.0"}, "devDependencies": {"@types/react": "^18.2.43", "@types/react-dom": "^18.2.17", "@types/node": "^20.10.5", "@types/express": "^4.17.21", "@types/cors": "^2.8.17", "@types/multer": "^1.4.11", "@types/bcryptjs": "^2.4.6", "@types/jsonwebtoken": "^9.0.5", "@typescript-eslint/eslint-plugin": "^6.14.0", "@typescript-eslint/parser": "^6.14.0", "@vitejs/plugin-react": "^4.2.1", "autoprefixer": "^10.4.16", "concurrently": "^8.2.2", "electron": "^28.1.0", "electron-builder": "^24.9.1", "eslint": "^8.55.0", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.5", "nodemon": "^3.0.2", "postcss": "^8.4.32", "tailwindcss": "^3.3.6", "ts-node": "^10.9.2", "typescript": "^5.2.2", "vite": "^5.0.8", "vitest": "^1.1.0", "wait-on": "^7.2.0"}, "build": {"appId": "com.xui.multimedia-manager", "productName": "XUI Multimedia Manager", "directories": {"output": "release"}, "files": ["dist/**/*", "node_modules/**/*", "package.json"], "win": {"target": [{"target": "portable", "arch": ["x64"]}, {"target": "nsis", "arch": ["x64"]}], "icon": "assets/icon.ico"}, "linux": {"target": [{"target": "AppImage", "arch": ["x64"]}]}, "mac": {"target": [{"target": "dmg", "arch": ["x64", "arm64"]}]}, "portable": {"artifactName": "${productName}-${version}-portable.${ext}"}}}