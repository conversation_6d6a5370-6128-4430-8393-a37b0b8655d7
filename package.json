{"name": "xui-multimedia-manager", "version": "1.0.0", "description": "Ultra-powerful multimedia file manager with VS Code-like interface", "main": "dist/main.js", "type": "module", "scripts": {"setup": "node setup.js", "dev": "concurrently \"npm run dev:backend\" \"npm run dev:frontend\"", "dev:backend": "nodemon src/backend/simple-server.js", "dev:frontend": "vite", "build": "npm run build:backend && npm run build:frontend", "build:backend": "tsc -p tsconfig.backend.json", "build:frontend": "vite build", "build:desktop": "npm run build && electron-builder", "build:portable": "npm run build && electron-builder --portable", "build:desktop-script": "node scripts/build-desktop.js", "preview": "vite preview", "start": "node dist/server.js", "electron": "electron .", "electron:dev": "concurrently \"npm run dev:backend\" \"wait-on http://localhost:5173 && electron .\"", "test": "vitest", "test:ui": "vitest --ui", "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "lint:fix": "eslint . --ext ts,tsx --fix", "clean": "rimraf dist release", "postinstall": "electron-builder install-app-deps"}, "keywords": ["multimedia", "file-manager", "xui", "tmdb", "m3u", "streaming"], "author": "XUI Multimedia Manager", "license": "MIT", "dependencies": {"@tanstack/react-query": "^5.17.0", "@tanstack/react-table": "^8.11.0", "axios": "^1.6.5", "bcryptjs": "^2.4.3", "cors": "^2.8.5", "dotenv": "^16.3.1", "express": "^4.18.2", "helmet": "^7.1.0", "joi": "^17.11.0", "jsonwebtoken": "^9.0.2", "multer": "^1.4.5-lts.1", "mysql2": "^3.6.5", "react": "^18.2.0", "react-dom": "^18.2.0", "react-router-dom": "^6.20.1", "socket.io": "^4.7.4", "socket.io-client": "^4.7.4", "winston": "^3.11.0"}, "devDependencies": {"@types/bcryptjs": "^2.4.6", "@types/cors": "^2.8.17", "@types/express": "^4.17.21", "@types/jsonwebtoken": "^9.0.5", "@types/multer": "^1.4.11", "@types/node": "^20.10.5", "@types/react": "^18.2.43", "@types/react-dom": "^18.2.17", "@typescript-eslint/eslint-plugin": "^6.14.0", "@typescript-eslint/parser": "^6.14.0", "@vitejs/plugin-react": "^4.2.1", "autoprefixer": "^10.4.16", "concurrently": "^8.2.2", "electron": "^28.1.0", "electron-builder": "^24.13.3", "eslint": "^8.55.0", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.5", "nodemon": "^3.0.2", "postcss": "^8.4.32", "rimraf": "^5.0.5", "tailwindcss": "^3.3.6", "ts-node": "^10.9.2", "typescript": "^5.2.2", "vite": "^5.0.8", "vitest": "^1.1.0", "wait-on": "^7.2.0"}, "build": {"appId": "com.xui.multimedia-manager", "productName": "XUI Multimedia Manager", "copyright": "Copyright © 2024 XUI Multimedia Manager", "directories": {"output": "release", "buildResources": "assets"}, "files": ["dist/**/*", "electron.js", "preload.js", "package.json", {"from": "node_modules", "filter": ["!**/node_modules/*/{CHANGELOG.md,README.md,README,readme.md,readme}", "!**/node_modules/*/{test,__tests__,tests,powered-test,example,examples}", "!**/node_modules/*.d.ts", "!**/node_modules/.bin", "!**/*.{iml,o,hprof,orig,pyc,pyo,rbc,swp,csproj,sln,xproj}", "!.editorconfig", "!**/._*", "!**/{.DS_Store,.git,.hg,.svn,CVS,RCS,SCCS,.gitignore,.gitattributes}", "!**/{__pycache__,thumbs.db,.flowconfig,.idea,.vs,.nyc_output}", "!**/{appveyor.yml,.travis.yml,circle.yml}", "!**/{npm-debug.log,yarn.lock,.yarn-integrity,.yarn-metadata.json}"]}], "extraResources": [{"from": "assets", "to": "assets", "filter": ["**/*"]}], "win": {"target": [{"target": "portable", "arch": ["x64"]}, {"target": "nsis", "arch": ["x64"]}], "icon": "assets/icon.ico", "requestedExecutionLevel": "asInvoker", "artifactName": "${productName}-${version}-${arch}.${ext}"}, "nsis": {"oneClick": false, "allowToChangeInstallationDirectory": true, "createDesktopShortcut": true, "createStartMenuShortcut": true, "shortcutName": "XUI Multimedia Manager"}, "portable": {"artifactName": "${productName}-${version}-portable.${ext}"}, "linux": {"target": [{"target": "AppImage", "arch": ["x64"]}, {"target": "deb", "arch": ["x64"]}], "icon": "assets/icon.png", "category": "AudioVideo", "synopsis": "Ultra-powerful multimedia file manager"}, "mac": {"target": [{"target": "dmg", "arch": ["x64", "arm64"]}], "icon": "assets/icon.icns", "category": "public.app-category.video"}, "dmg": {"title": "${productName} ${version}", "icon": "assets/icon.icns"}, "compression": "maximum", "removePackageScripts": true}}