# Deployment Guide

This guide covers different deployment scenarios for the XUI Multimedia Manager.

## 🖥️ Desktop Application (Portable .exe)

### Building the Portable Executable

1. **Install dependencies**
   ```bash
   npm install
   ```

2. **Build the application**
   ```bash
   npm run build:portable
   ```

3. **Find your executable**
   - The portable .exe will be in the `release` folder
   - File name: `XUI Multimedia Manager-1.0.0-portable.exe`
   - No installation required - just run the .exe

### Desktop App Features
- ✅ No installation required
- ✅ Runs from any folder
- ✅ Includes both frontend and backend
- ✅ Auto-starts backend server
- ✅ Self-contained with Node.js runtime

## 🌐 Web Application

### Local Web Server

1. **Build for production**
   ```bash
   npm run build
   ```

2. **Start the server**
   ```bash
   npm start
   ```

3. **Access the application**
   - Open http://localhost:3001 in your browser

### Docker Deployment

1. **Create Dockerfile**
   ```dockerfile
   FROM node:18-alpine
   
   WORKDIR /app
   COPY package*.json ./
   RUN npm ci --only=production
   
   COPY dist ./dist
   COPY .env ./
   
   EXPOSE 3001
   CMD ["npm", "start"]
   ```

2. **Build and run**
   ```bash
   docker build -t xui-multimedia-manager .
   docker run -p 3001:3001 -v /path/to/media:/media xui-multimedia-manager
   ```

### Nginx Reverse Proxy

```nginx
server {
    listen 80;
    server_name your-domain.com;
    
    location / {
        proxy_pass http://localhost:3001;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
    }
    
    # WebSocket support
    location /socket.io/ {
        proxy_pass http://localhost:3001;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
}
```

## 🗄️ Database Setup

### MySQL/MariaDB Configuration

1. **Create database**
   ```sql
   CREATE DATABASE xui_database CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
   CREATE USER 'xui_user'@'localhost' IDENTIFIED BY 'secure_password';
   GRANT ALL PRIVILEGES ON xui_database.* TO 'xui_user'@'localhost';
   FLUSH PRIVILEGES;
   ```

2. **Import XUI schema**
   - Use your existing XUI database
   - Or import the schema from `tables.txt`

3. **Configure connection**
   ```env
   DB_HOST=localhost
   DB_PORT=3306
   DB_USER=xui_user
   DB_PASSWORD=secure_password
   DB_NAME=xui_database
   ```

### Remote Database

For remote database access:

```env
DB_HOST=your-remote-host.com
DB_PORT=3306
DB_USER=remote_user
DB_PASSWORD=remote_password
DB_NAME=xui_database
```

## 🔧 Environment Configuration

### Production Environment Variables

```env
# Server
NODE_ENV=production
PORT=3001

# Database
DB_HOST=localhost
DB_PORT=3306
DB_USER=xui_user
DB_PASSWORD=secure_password
DB_NAME=xui_database

# Security
JWT_SECRET=your_super_secure_jwt_secret_here
BCRYPT_ROUNDS=12

# TMDB
TMDB_API_KEY=your_tmdb_api_key

# File System
MEDIA_ROOT_PATH=/path/to/your/media
TEMP_UPLOAD_PATH=/tmp/xui-uploads
MAX_FILE_SIZE=1073741824

# Logging
LOG_LEVEL=info
LOG_FILE=/var/log/xui-multimedia-manager.log

# Features
ENABLE_FILE_OPERATIONS=true
ENABLE_DATABASE_CLEANUP=true
ENABLE_M3U_PARSING=true
```

## 🚀 Production Deployment

### PM2 Process Manager

1. **Install PM2**
   ```bash
   npm install -g pm2
   ```

2. **Create ecosystem file**
   ```javascript
   // ecosystem.config.js
   module.exports = {
     apps: [{
       name: 'xui-multimedia-manager',
       script: 'dist/main.js',
       instances: 1,
       autorestart: true,
       watch: false,
       max_memory_restart: '1G',
       env: {
         NODE_ENV: 'production',
         PORT: 3001
       }
     }]
   }
   ```

3. **Start with PM2**
   ```bash
   pm2 start ecosystem.config.js
   pm2 save
   pm2 startup
   ```

### Systemd Service

1. **Create service file**
   ```ini
   # /etc/systemd/system/xui-multimedia-manager.service
   [Unit]
   Description=XUI Multimedia Manager
   After=network.target
   
   [Service]
   Type=simple
   User=www-data
   WorkingDirectory=/opt/xui-multimedia-manager
   ExecStart=/usr/bin/node dist/main.js
   Restart=on-failure
   Environment=NODE_ENV=production
   Environment=PORT=3001
   
   [Install]
   WantedBy=multi-user.target
   ```

2. **Enable and start**
   ```bash
   sudo systemctl enable xui-multimedia-manager
   sudo systemctl start xui-multimedia-manager
   ```

## 📊 Monitoring

### Health Checks

The application provides health check endpoints:

- `GET /api/health` - Application health
- `GET /api/dashboard/health` - System health

### Logging

Logs are written to:
- Console (development)
- File specified in `LOG_FILE` environment variable
- Separate error log file

### Metrics

Monitor these key metrics:
- Database connection status
- Memory usage
- Response times
- Error rates
- Active WebSocket connections

## 🔒 Security Considerations

### Production Security

1. **Use HTTPS**
   - Configure SSL certificates
   - Redirect HTTP to HTTPS

2. **Database Security**
   - Use strong passwords
   - Limit database user privileges
   - Enable SSL for database connections

3. **File System**
   - Restrict file upload sizes
   - Validate file types
   - Use secure temporary directories

4. **Network Security**
   - Configure firewall rules
   - Use VPN for remote access
   - Implement rate limiting

### Environment Security

```env
# Use strong secrets
JWT_SECRET=very_long_random_string_here
DB_PASSWORD=complex_password_with_special_chars

# Restrict file operations in production
ENABLE_FILE_OPERATIONS=false

# Enable security features
HELMET_ENABLED=true
CORS_ORIGIN=https://your-domain.com
```

## 🔄 Updates and Maintenance

### Updating the Application

1. **Backup database**
   ```bash
   mysqldump -u xui_user -p xui_database > backup.sql
   ```

2. **Stop the application**
   ```bash
   pm2 stop xui-multimedia-manager
   ```

3. **Update code**
   ```bash
   git pull origin main
   npm install
   npm run build
   ```

4. **Start the application**
   ```bash
   pm2 start xui-multimedia-manager
   ```

### Database Maintenance

- Regular backups
- Index optimization
- Clean up old logs
- Monitor disk space

## 🆘 Troubleshooting

### Common Issues

1. **Database connection failed**
   - Check credentials in .env
   - Verify database server is running
   - Check network connectivity

2. **Port already in use**
   - Change PORT in .env
   - Kill existing processes

3. **File upload issues**
   - Check TEMP_UPLOAD_PATH permissions
   - Verify MAX_FILE_SIZE setting

4. **Memory issues**
   - Monitor memory usage
   - Adjust PM2 max_memory_restart
   - Optimize database queries

### Log Analysis

Check logs for errors:
```bash
# PM2 logs
pm2 logs xui-multimedia-manager

# System logs
journalctl -u xui-multimedia-manager

# Application logs
tail -f /var/log/xui-multimedia-manager.log
```
