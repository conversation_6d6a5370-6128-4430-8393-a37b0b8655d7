import React from 'react'

interface ContentDistributionData {
  type_name: string
  count: number
}

interface ContentDistributionChartProps {
  data: ContentDistributionData[]
}

const ContentDistributionChart: React.FC<ContentDistributionChartProps> = ({ data }) => {
  if (!data || data.length === 0) {
    return (
      <div className="flex items-center justify-center h-64 text-vscode-text-muted">
        No data available
      </div>
    )
  }

  const maxCount = Math.max(...data.map(item => item.count))
  const colors = ['#00D4FF', '#FF00D4', '#D4FF00', '#FF6B00']

  return (
    <div className="h-64 flex items-end justify-center space-x-4">
      {data.map((item, index) => {
        const height = (item.count / maxCount) * 200
        const color = colors[index % colors.length]
        
        return (
          <div key={item.type_name} className="flex flex-col items-center">
            <div className="text-xs text-vscode-text-muted mb-2 font-mono">
              {item.count.toLocaleString()}
            </div>
            <div
              className="w-16 rounded-t-sm transition-all duration-500 hover:opacity-80"
              style={{
                height: `${height}px`,
                backgroundColor: color,
                minHeight: '20px'
              }}
            />
            <div className="text-xs text-vscode-text mt-2 text-center max-w-16">
              {item.type_name}
            </div>
          </div>
        )
      })}
    </div>
  )
}

export default ContentDistributionChart
