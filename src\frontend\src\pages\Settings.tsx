import React from 'react'

const Settings: React.FC = () => {
  return (
    <div className="p-6">
      <div className="flex items-center justify-between mb-6">
        <div>
          <h1 className="text-2xl font-bold text-vscode-text">Settings</h1>
          <p className="text-vscode-text-muted">
            Configure your multimedia manager preferences
          </p>
        </div>
      </div>

      <div className="space-y-6">
        {/* Database Settings */}
        <div className="card">
          <div className="card-header">
            <h2 className="text-lg font-semibold text-vscode-text">Database Configuration</h2>
          </div>
          <div className="card-body space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-vscode-text mb-2">
                  Host
                </label>
                <input
                  type="text"
                  defaultValue="localhost"
                  className="input-primary w-full"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-vscode-text mb-2">
                  Port
                </label>
                <input
                  type="number"
                  defaultValue="3306"
                  className="input-primary w-full"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-vscode-text mb-2">
                  Database Name
                </label>
                <input
                  type="text"
                  defaultValue="xui_database"
                  className="input-primary w-full"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-vscode-text mb-2">
                  Username
                </label>
                <input
                  type="text"
                  defaultValue="root"
                  className="input-primary w-full"
                />
              </div>
            </div>
            <div>
              <label className="block text-sm font-medium text-vscode-text mb-2">
                Password
              </label>
              <input
                type="password"
                className="input-primary w-full"
              />
            </div>
            <div className="flex space-x-3">
              <button className="btn-primary">Test Connection</button>
              <button className="btn-secondary">Save Settings</button>
            </div>
          </div>
        </div>

        {/* Cleanup Settings */}
        <div className="card">
          <div className="card-header">
            <h2 className="text-lg font-semibold text-vscode-text">Cleanup Preferences</h2>
          </div>
          <div className="card-body space-y-4">
            <div className="space-y-3">
              <label className="flex items-center space-x-3">
                <input type="checkbox" defaultChecked className="rounded" />
                <span className="text-vscode-text">Never delete 4K versions</span>
              </label>
              <label className="flex items-center space-x-3">
                <input type="checkbox" defaultChecked className="rounded" />
                <span className="text-vscode-text">Never delete symlinks</span>
              </label>
              <label className="flex items-center space-x-3">
                <input type="checkbox" defaultChecked className="rounded" />
                <span className="text-vscode-text">Prioritize deleting direct_source duplicates</span>
              </label>
              <label className="flex items-center space-x-3">
                <input type="checkbox" defaultChecked className="rounded" />
                <span className="text-vscode-text">Always ask for confirmation in smart mode</span>
              </label>
            </div>
          </div>
        </div>

        {/* File System Settings */}
        <div className="card">
          <div className="card-header">
            <h2 className="text-lg font-semibold text-vscode-text">File System</h2>
          </div>
          <div className="card-body space-y-4">
            <div>
              <label className="block text-sm font-medium text-vscode-text mb-2">
                Media Root Path
              </label>
              <input
                type="text"
                defaultValue="/path/to/your/media/files"
                className="input-primary w-full"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-vscode-text mb-2">
                Temporary Upload Path
              </label>
              <input
                type="text"
                defaultValue="./temp/uploads"
                className="input-primary w-full"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-vscode-text mb-2">
                Max File Size (MB)
              </label>
              <input
                type="number"
                defaultValue="1024"
                className="input-primary w-full"
              />
            </div>
          </div>
        </div>

        {/* TMDB Settings */}
        <div className="card">
          <div className="card-header">
            <h2 className="text-lg font-semibold text-vscode-text">TMDB Integration</h2>
          </div>
          <div className="card-body space-y-4">
            <div>
              <label className="block text-sm font-medium text-vscode-text mb-2">
                API Key
              </label>
              <input
                type="text"
                defaultValue="201066b4b17391d478e55247f43eed64"
                className="input-primary w-full font-mono"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-vscode-text mb-2">
                Default Language
              </label>
              <select className="input-primary w-full">
                <option value="en">English</option>
                <option value="es">Spanish</option>
                <option value="fr">French</option>
                <option value="de">German</option>
              </select>
            </div>
          </div>
        </div>

        {/* Application Settings */}
        <div className="card">
          <div className="card-header">
            <h2 className="text-lg font-semibold text-vscode-text">Application</h2>
          </div>
          <div className="card-body space-y-4">
            <div className="space-y-3">
              <label className="flex items-center space-x-3">
                <input type="checkbox" defaultChecked className="rounded" />
                <span className="text-vscode-text">Enable file operations</span>
              </label>
              <label className="flex items-center space-x-3">
                <input type="checkbox" defaultChecked className="rounded" />
                <span className="text-vscode-text">Enable database cleanup</span>
              </label>
              <label className="flex items-center space-x-3">
                <input type="checkbox" defaultChecked className="rounded" />
                <span className="text-vscode-text">Enable M3U parsing</span>
              </label>
              <label className="flex items-center space-x-3">
                <input type="checkbox" className="rounded" />
                <span className="text-vscode-text">Enable debug logging</span>
              </label>
            </div>
            <div>
              <label className="block text-sm font-medium text-vscode-text mb-2">
                Log Level
              </label>
              <select className="input-primary w-full">
                <option value="error">Error</option>
                <option value="warn">Warning</option>
                <option value="info" selected>Info</option>
                <option value="debug">Debug</option>
              </select>
            </div>
          </div>
        </div>

        {/* Save Button */}
        <div className="flex justify-end space-x-3">
          <button className="btn-secondary">Reset to Defaults</button>
          <button className="btn-primary">Save All Settings</button>
        </div>
      </div>
    </div>
  )
}

export default Settings
