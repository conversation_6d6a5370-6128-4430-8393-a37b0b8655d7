import React, { useState } from 'react'
import { useQuery } from '@tanstack/react-query'
import { seriesApi } from '../services/api'

const Series: React.FC = () => {
  const [activeTab, setActiveTab] = useState<'all' | 'orphaned' | 'duplicates'>('all')

  const { data: allSeries, isLoading: allSeriesLoading } = useQuery({
    queryKey: ['series'],
    queryFn: seriesApi.getAll,
    enabled: activeTab === 'all',
  })

  const { data: orphanedSeries, isLoading: orphanedSeriesLoading } = useQuery({
    queryKey: ['series-without-episodes'],
    queryFn: seriesApi.getWithoutEpisodes,
    enabled: activeTab === 'orphaned',
  })

  const { data: orphanedEpisodes, isLoading: orphanedEpisodesLoading } = useQuery({
    queryKey: ['orphaned-episodes'],
    queryFn: seriesApi.getOrphanEpisodes,
    enabled: activeTab === 'orphaned',
  })

  const { data: duplicateEpisodes, isLoading: duplicateEpisodesLoading } = useQuery({
    queryKey: ['duplicate-episodes'],
    queryFn: seriesApi.getDuplicateEpisodes,
    enabled: activeTab === 'duplicates',
  })

  const tabs = [
    { id: 'all', label: 'All Series', count: allSeries?.data?.length },
    { id: 'orphaned', label: 'Orphaned Content', count: (orphanedSeries?.data?.length || 0) + (orphanedEpisodes?.data?.length || 0) },
    { id: 'duplicates', label: 'Duplicate Episodes', count: duplicateEpisodes?.data?.length },
  ]

  return (
    <div className="p-6">
      <div className="flex items-center justify-between mb-6">
        <div>
          <h1 className="text-2xl font-bold text-vscode-text">Series</h1>
          <p className="text-vscode-text-muted">
            Manage your series collection and organize episodes
          </p>
        </div>
        <div className="flex space-x-3">
          <button className="btn-primary">
            Clean Orphans
          </button>
          <button className="btn-secondary">
            Scan Library
          </button>
        </div>
      </div>

      {/* Tabs */}
      <div className="flex space-x-1 mb-6">
        {tabs.map((tab) => (
          <button
            key={tab.id}
            onClick={() => setActiveTab(tab.id as any)}
            className={`px-4 py-2 rounded-sm font-medium transition-colors ${
              activeTab === tab.id
                ? 'bg-vscode-accent text-white'
                : 'bg-vscode-panel text-vscode-text hover:bg-vscode-border'
            }`}
          >
            {tab.label}
            {tab.count !== undefined && (
              <span className="ml-2 px-2 py-1 bg-vscode-border rounded-full text-xs">
                {tab.count}
              </span>
            )}
          </button>
        ))}
      </div>

      {/* Content */}
      <div className="card">
        <div className="card-body">
          {activeTab === 'all' && (
            <>
              {allSeriesLoading ? (
                <div className="flex items-center justify-center py-12">
                  <div className="spinner"></div>
                  <span className="ml-2 text-vscode-text-muted">Loading series...</span>
                </div>
              ) : (
                <div className="overflow-x-auto">
                  <table className="table">
                    <thead>
                      <tr>
                        <th>Title</th>
                        <th>Episodes</th>
                        <th>Seasons</th>
                        <th>Year</th>
                        <th>Status</th>
                        <th>Actions</th>
                      </tr>
                    </thead>
                    <tbody>
                      {allSeries?.data?.slice(0, 50).map((series: any) => (
                        <tr key={series.id}>
                          <td className="font-medium">{series.title}</td>
                          <td>{series.episode_count || 0}</td>
                          <td>{series.complete_seasons?.length || 0}</td>
                          <td>{series.year || 'N/A'}</td>
                          <td>
                            <span className={`px-2 py-1 rounded-sm text-xs ${
                              series.episode_count > 0 
                                ? 'bg-green-500/20 text-green-400' 
                                : 'bg-yellow-500/20 text-yellow-400'
                            }`}>
                              {series.episode_count > 0 ? 'Complete' : 'Incomplete'}
                            </span>
                          </td>
                          <td>
                            <button className="btn-danger text-xs">
                              Delete
                            </button>
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              )}
            </>
          )}

          {activeTab === 'orphaned' && (
            <>
              {orphanedSeriesLoading || orphanedEpisodesLoading ? (
                <div className="flex items-center justify-center py-12">
                  <div className="spinner"></div>
                  <span className="ml-2 text-vscode-text-muted">Loading orphaned content...</span>
                </div>
              ) : (
                <div className="space-y-6">
                  {/* Series without episodes */}
                  <div>
                    <h3 className="text-lg font-semibold text-vscode-text mb-3">
                      Series Without Episodes ({orphanedSeries?.data?.length || 0})
                    </h3>
                    {orphanedSeries?.data?.length > 0 ? (
                      <div className="space-y-2">
                        {orphanedSeries.data.map((series: any) => (
                          <div key={series.id} className="flex items-center justify-between bg-vscode-bg p-3 rounded-sm">
                            <div>
                              <span className="text-vscode-text font-medium">{series.title}</span>
                              <div className="text-xs text-vscode-text-muted">
                                Year: {series.year || 'Unknown'} • TMDB ID: {series.tmdb_id || 'None'}
                              </div>
                            </div>
                            <button className="btn-danger text-xs">
                              Delete Series
                            </button>
                          </div>
                        ))}
                      </div>
                    ) : (
                      <p className="text-vscode-text-muted">No series without episodes found.</p>
                    )}
                  </div>

                  {/* Episodes without series */}
                  <div>
                    <h3 className="text-lg font-semibold text-vscode-text mb-3">
                      Episodes Without Series ({orphanedEpisodes?.data?.length || 0})
                    </h3>
                    {orphanedEpisodes?.data?.length > 0 ? (
                      <div className="space-y-2">
                        {orphanedEpisodes.data.map((episode: any) => (
                          <div key={episode.id} className="flex items-center justify-between bg-vscode-bg p-3 rounded-sm">
                            <div>
                              <span className="text-vscode-text font-medium">
                                {episode.stream_display_name || `S${episode.season_num}E${episode.episode_num}`}
                              </span>
                              <div className="text-xs text-vscode-text-muted">
                                Season {episode.season_num} • Episode {episode.episode_num}
                              </div>
                            </div>
                            <button className="btn-danger text-xs">
                              Delete Episode
                            </button>
                          </div>
                        ))}
                      </div>
                    ) : (
                      <p className="text-vscode-text-muted">No orphaned episodes found.</p>
                    )}
                  </div>
                </div>
              )}
            </>
          )}

          {activeTab === 'duplicates' && (
            <>
              {duplicateEpisodesLoading ? (
                <div className="flex items-center justify-center py-12">
                  <div className="spinner"></div>
                  <span className="ml-2 text-vscode-text-muted">Finding duplicate episodes...</span>
                </div>
              ) : (
                <div className="space-y-4">
                  {duplicateEpisodes?.data?.length > 0 ? (
                    duplicateEpisodes.data.map((group: any, index: number) => (
                      <div key={index} className="border border-vscode-border rounded-sm p-4">
                        <h3 className="font-semibold text-vscode-text mb-2">
                          {group.series_title} - S{group.season_num}E{group.episode_num}
                        </h3>
                        <div className="space-y-2">
                          {group.duplicates.map((episode: any) => (
                            <div key={episode.id} className="flex items-center justify-between bg-vscode-bg p-2 rounded-sm">
                              <div>
                                <span className="text-vscode-text">{episode.stream_display_name}</span>
                                <div className="text-xs text-vscode-text-muted">
                                  Stream ID: {episode.stream_id}
                                </div>
                              </div>
                              <button className="btn-danger text-xs">
                                Delete
                              </button>
                            </div>
                          ))}
                        </div>
                      </div>
                    ))
                  ) : (
                    <div className="text-center py-12">
                      <div className="text-4xl mb-4">🎉</div>
                      <h3 className="text-lg font-semibold text-vscode-text mb-2">
                        No Duplicate Episodes Found
                      </h3>
                      <p className="text-vscode-text-muted">
                        Your series collection is well organized!
                      </p>
                    </div>
                  )}
                </div>
              )}
            </>
          )}
        </div>
      </div>
    </div>
  )
}

export default Series
