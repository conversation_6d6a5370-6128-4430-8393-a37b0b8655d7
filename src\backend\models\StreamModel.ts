import { db } from '../database/connection.js'
import { Stream, StreamType, MovieInfo, DuplicateGroup } from '../../types/database.js'
import logger from '../utils/logger.js'

export class StreamModel {
  // Get all streams with type information
  static async getAllStreams(): Promise<Stream[]> {
    const sql = `
      SELECT s.*, st.type_name, sc.category_name
      FROM streams s
      LEFT JOIN streams_types st ON s.type = st.type_id
      LEFT JOIN streams_categories sc ON s.category_id = sc.id
      ORDER BY s.id
    `
    return await db.query<Stream>(sql)
  }

  // Get streams by type
  static async getStreamsByType(typeId: number): Promise<Stream[]> {
    const sql = `
      SELECT s.*, st.type_name, sc.category_name
      FROM streams s
      LEFT JOIN streams_types st ON s.type = st.type_id
      LEFT JOIN streams_categories sc ON s.category_id = sc.id
      WHERE s.type = ?
      ORDER BY s.stream_display_name
    `
    return await db.query<Stream>(sql, [typeId])
  }

  // Get movies (assuming type 1 is movies)
  static async getMovies(): Promise<MovieInfo[]> {
    const sql = `
      SELECT s.*, st.type_name, sc.category_name
      FROM streams s
      LEFT JOIN streams_types st ON s.type = st.type_id
      LEFT JOIN streams_categories sc ON s.category_id = sc.id
      WHERE st.type_key = 'movie' OR s.type = 1
      ORDER BY s.stream_display_name
    `
    return await db.query<MovieInfo>(sql)
  }

  // Find duplicate movies based on title similarity
  static async findDuplicateMovies(): Promise<DuplicateGroup[]> {
    const sql = `
      SELECT 
        s1.id as id1,
        s1.stream_display_name as name1,
        s1.direct_source as direct1,
        s1.movie_symlink as symlink1,
        s1.stream_source as source1,
        s2.id as id2,
        s2.stream_display_name as name2,
        s2.direct_source as direct2,
        s2.movie_symlink as symlink2,
        s2.stream_source as source2
      FROM streams s1
      JOIN streams s2 ON s1.id < s2.id
      WHERE (s1.type = 1 OR s1.type IN (SELECT type_id FROM streams_types WHERE type_key = 'movie'))
        AND (s2.type = 1 OR s2.type IN (SELECT type_id FROM streams_types WHERE type_key = 'movie'))
        AND (
          SOUNDEX(s1.stream_display_name) = SOUNDEX(s2.stream_display_name)
          OR LEVENSHTEIN(s1.stream_display_name, s2.stream_display_name) < 3
          OR s1.stream_display_name LIKE CONCAT('%', SUBSTRING_INDEX(s2.stream_display_name, ' ', 3), '%')
        )
      ORDER BY s1.stream_display_name
    `
    
    try {
      const duplicates = await db.query(sql)
      return this.groupDuplicates(duplicates)
    } catch (error) {
      // Fallback to simpler query if LEVENSHTEIN function is not available
      logger.warn('Advanced duplicate detection failed, using basic method')
      return this.findBasicDuplicates()
    }
  }

  // Basic duplicate detection without advanced functions
  private static async findBasicDuplicates(): Promise<DuplicateGroup[]> {
    const sql = `
      SELECT s.*, st.type_name
      FROM streams s
      LEFT JOIN streams_types st ON s.type = st.type_id
      WHERE (s.type = 1 OR st.type_key = 'movie')
      ORDER BY s.stream_display_name
    `
    
    const movies = await db.query<MovieInfo>(sql)
    const groups: DuplicateGroup[] = []
    const processed = new Set<number>()

    for (let i = 0; i < movies.length; i++) {
      if (processed.has(movies[i].id)) continue

      const duplicates = [movies[i]]
      processed.add(movies[i].id)

      for (let j = i + 1; j < movies.length; j++) {
        if (processed.has(movies[j].id)) continue

        if (this.isSimilarTitle(movies[i].stream_display_name, movies[j].stream_display_name)) {
          duplicates.push(movies[j])
          processed.add(movies[j].id)
        }
      }

      if (duplicates.length > 1) {
        groups.push({
          id: `group_${i}`,
          title: movies[i].stream_display_name,
          items: duplicates,
          total_size: 0, // Will be calculated later
          recommended_action: this.getRecommendedAction(duplicates)
        })
      }
    }

    return groups
  }

  private static isSimilarTitle(title1: string, title2: string): boolean {
    const clean1 = title1.toLowerCase().replace(/[^\w\s]/g, '').trim()
    const clean2 = title2.toLowerCase().replace(/[^\w\s]/g, '').trim()
    
    // Exact match
    if (clean1 === clean2) return true
    
    // Check if one contains the other (for different quality versions)
    if (clean1.includes(clean2) || clean2.includes(clean1)) return true
    
    // Check first 3 words similarity
    const words1 = clean1.split(' ').slice(0, 3).join(' ')
    const words2 = clean2.split(' ').slice(0, 3).join(' ')
    
    return words1 === words2
  }

  private static groupDuplicates(duplicates: any[]): DuplicateGroup[] {
    // Implementation for grouping duplicates from SQL result
    const groups: Map<string, MovieInfo[]> = new Map()
    
    duplicates.forEach(dup => {
      const key = dup.name1.toLowerCase().replace(/[^\w]/g, '')
      if (!groups.has(key)) {
        groups.set(key, [])
      }
      // Add logic to group duplicates
    })

    return Array.from(groups.entries()).map(([key, items], index) => ({
      id: `group_${index}`,
      title: items[0].stream_display_name,
      items,
      total_size: 0,
      recommended_action: this.getRecommendedAction(items)
    }))
  }

  private static getRecommendedAction(items: MovieInfo[]): 'keep_4k' | 'keep_symlink' | 'keep_best_quality' | 'manual_review' {
    // Check for 4K versions
    const has4K = items.some(item => 
      item.stream_display_name.toLowerCase().includes('4k') ||
      item.stream_display_name.toLowerCase().includes('2160p')
    )
    
    if (has4K) return 'keep_4k'
    
    // Check for symlinks
    const hasSymlink = items.some(item => item.movie_symlink === 1)
    if (hasSymlink) return 'keep_symlink'
    
    // Default to manual review for complex cases
    return 'manual_review'
  }

  // Delete stream by ID
  static async deleteStream(id: number): Promise<boolean> {
    try {
      await db.transaction(async (connection) => {
        // Delete related records first
        await connection.execute('DELETE FROM streams_servers WHERE stream_id = ?', [id])
        await connection.execute('DELETE FROM streams_options WHERE stream_id = ?', [id])
        await connection.execute('DELETE FROM streams_episodes WHERE stream_id = ?', [id])
        
        // Delete the main stream record
        await connection.execute('DELETE FROM streams WHERE id = ?', [id])
      })
      
      logger.info(`Stream ${id} deleted successfully`)
      return true
    } catch (error) {
      logger.error(`Failed to delete stream ${id}:`, error)
      return false
    }
  }

  // Get stream types
  static async getStreamTypes(): Promise<StreamType[]> {
    const sql = 'SELECT * FROM streams_types ORDER BY type_id'
    return await db.query<StreamType>(sql)
  }

  // Get streams count by type
  static async getStreamCountByType(): Promise<{ type_name: string; count: number }[]> {
    const sql = `
      SELECT st.type_name, COUNT(s.id) as count
      FROM streams_types st
      LEFT JOIN streams s ON st.type_id = s.type
      GROUP BY st.type_id, st.type_name
      ORDER BY count DESC
    `
    return await db.query(sql)
  }

  // Get dashboard statistics
  static async getDashboardStats(): Promise<{
    total_streams: number;
    live_channels: number;
    movies: number;
    series: number;
    unknown: number;
  }> {
    const sql = `
      SELECT
        COUNT(*) as total_streams,
        SUM(CASE WHEN st.live = 1 THEN 1 ELSE 0 END) as live_channels,
        SUM(CASE WHEN st.type_key = 'movie' THEN 1 ELSE 0 END) as movies,
        SUM(CASE WHEN st.type_key = 'series' THEN 1 ELSE 0 END) as series,
        SUM(CASE WHEN st.type_key IS NULL OR st.type_key NOT IN ('movie', 'series') AND st.live = 0 THEN 1 ELSE 0 END) as unknown
      FROM streams s
      LEFT JOIN streams_types st ON s.type = st.type_id
    `
    const result = await db.queryOne(sql)
    return result || { total_streams: 0, live_channels: 0, movies: 0, series: 0, unknown: 0 }
  }
}
