import { Router } from 'express'
import { StreamModel } from '../models/StreamModel.js'
import logger from '../utils/logger.js'

const router = Router()

// Get all streams
router.get('/', async (req, res) => {
  try {
    const { type, limit = 100, offset = 0 } = req.query
    
    let streams
    if (type) {
      streams = await StreamModel.getStreamsByType(Number(type))
    } else {
      streams = await StreamModel.getAllStreams()
    }

    // Apply pagination
    const startIndex = Number(offset)
    const endIndex = startIndex + Number(limit)
    const paginatedStreams = streams.slice(startIndex, endIndex)

    res.json({
      data: paginatedStreams,
      pagination: {
        total: streams.length,
        limit: Number(limit),
        offset: Number(offset),
        has_more: endIndex < streams.length
      }
    })
  } catch (error) {
    logger.error('Error fetching streams:', error)
    res.status(500).json({
      error: 'Failed to fetch streams',
      message: error.message
    })
  }
})

// Get movies
router.get('/movies', async (req, res) => {
  try {
    const movies = await StreamModel.getMovies()
    res.json(movies)
  } catch (error) {
    logger.error('Error fetching movies:', error)
    res.status(500).json({
      error: 'Failed to fetch movies',
      message: error.message
    })
  }
})

// Find duplicate movies
router.get('/movies/duplicates', async (req, res) => {
  try {
    const duplicates = await StreamModel.findDuplicateMovies()
    res.json(duplicates)
  } catch (error) {
    logger.error('Error finding duplicate movies:', error)
    res.status(500).json({
      error: 'Failed to find duplicate movies',
      message: error.message
    })
  }
})

// Delete stream
router.delete('/:id', async (req, res) => {
  try {
    const { id } = req.params
    const success = await StreamModel.deleteStream(Number(id))
    
    if (success) {
      res.json({ message: 'Stream deleted successfully' })
    } else {
      res.status(500).json({ error: 'Failed to delete stream' })
    }
  } catch (error) {
    logger.error('Error deleting stream:', error)
    res.status(500).json({
      error: 'Failed to delete stream',
      message: error.message
    })
  }
})

// Get stream types
router.get('/types', async (req, res) => {
  try {
    const types = await StreamModel.getStreamTypes()
    res.json(types)
  } catch (error) {
    logger.error('Error fetching stream types:', error)
    res.status(500).json({
      error: 'Failed to fetch stream types',
      message: error.message
    })
  }
})

// Bulk delete streams
router.post('/bulk-delete', async (req, res) => {
  try {
    const { stream_ids } = req.body
    
    if (!Array.isArray(stream_ids) || stream_ids.length === 0) {
      return res.status(400).json({
        error: 'Invalid request',
        message: 'stream_ids must be a non-empty array'
      })
    }

    const results = []
    for (const id of stream_ids) {
      const success = await StreamModel.deleteStream(Number(id))
      results.push({ id, success })
    }

    const successCount = results.filter(r => r.success).length
    const failureCount = results.length - successCount

    res.json({
      message: `Bulk delete completed`,
      results: {
        total: results.length,
        successful: successCount,
        failed: failureCount
      },
      details: results
    })
  } catch (error) {
    logger.error('Error in bulk delete:', error)
    res.status(500).json({
      error: 'Failed to perform bulk delete',
      message: error.message
    })
  }
})

export default router
