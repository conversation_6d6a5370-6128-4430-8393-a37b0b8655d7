import express from 'express'
import cors from 'cors'
import helmet from 'helmet'
import { createServer } from 'http'
import { Server as SocketIOServer } from 'socket.io'
import path from 'path'
import { fileURLToPath } from 'url'

import { db } from './database/connection.js'
import { serverConfig } from './config/database.js'
import logger from './utils/logger.js'

// Import routes
import streamRoutes from './routes/streams.js'
import seriesRoutes from './routes/series.js'
import m3uRoutes from './routes/m3u.js'
import dashboardRoutes from './routes/dashboard.js'

const __filename = fileURLToPath(import.meta.url)
const __dirname = path.dirname(__filename)

class Server {
  private app: express.Application
  private httpServer: any
  private io: SocketIOServer
  private port: number

  constructor() {
    this.app = express()
    this.port = serverConfig.port
    this.httpServer = createServer(this.app)
    this.io = new SocketIOServer(this.httpServer, {
      cors: {
        origin: process.env.NODE_ENV === 'development' ? 'http://localhost:5173' : false,
        methods: ['GET', 'POST']
      }
    })

    this.initializeMiddleware()
    this.initializeRoutes()
    this.initializeSocketIO()
    this.initializeErrorHandling()
  }

  private initializeMiddleware(): void {
    // Security middleware
    this.app.use(helmet({
      contentSecurityPolicy: {
        directives: {
          defaultSrc: ["'self'"],
          styleSrc: ["'self'", "'unsafe-inline'"],
          scriptSrc: ["'self'"],
          imgSrc: ["'self'", "data:", "https:"],
          connectSrc: ["'self'", "ws:", "wss:"],
        },
      },
    }))

    // CORS
    this.app.use(cors({
      origin: process.env.NODE_ENV === 'development' ? 'http://localhost:5173' : false,
      credentials: true
    }))

    // Body parsing
    this.app.use(express.json({ limit: '10mb' }))
    this.app.use(express.urlencoded({ extended: true, limit: '10mb' }))

    // Static files (for production)
    if (process.env.NODE_ENV === 'production') {
      const frontendPath = path.join(__dirname, '../frontend')
      this.app.use(express.static(frontendPath))
    }

    // Request logging
    this.app.use((req, res, next) => {
      logger.info(`${req.method} ${req.path}`, {
        ip: req.ip,
        userAgent: req.get('User-Agent')
      })
      next()
    })
  }

  private initializeRoutes(): void {
    // Health check
    this.app.get('/api/health', async (req, res) => {
      const dbHealth = await db.healthCheck()
      res.json({
        status: 'ok',
        timestamp: new Date().toISOString(),
        database: dbHealth ? 'connected' : 'disconnected',
        version: '1.0.0'
      })
    })

    // API routes
    this.app.use('/api/streams', streamRoutes)
    this.app.use('/api/series', seriesRoutes)
    this.app.use('/api/m3u', m3uRoutes)
    this.app.use('/api/dashboard', dashboardRoutes)

    // Serve frontend for all other routes (SPA)
    if (process.env.NODE_ENV === 'production') {
      this.app.get('*', (req, res) => {
        res.sendFile(path.join(__dirname, '../frontend/index.html'))
      })
    }
  }

  private initializeSocketIO(): void {
    this.io.on('connection', (socket) => {
      logger.info(`Client connected: ${socket.id}`)

      socket.on('disconnect', () => {
        logger.info(`Client disconnected: ${socket.id}`)
      })

      // Join rooms for real-time updates
      socket.on('join-room', (room: string) => {
        socket.join(room)
        logger.info(`Client ${socket.id} joined room: ${room}`)
      })

      // Handle cleanup operations
      socket.on('start-cleanup', async (data) => {
        try {
          socket.emit('cleanup-progress', { status: 'started', message: 'Cleanup operation started' })
          // Cleanup logic will be implemented in the cleanup service
          socket.emit('cleanup-progress', { status: 'completed', message: 'Cleanup completed successfully' })
        } catch (error) {
          socket.emit('cleanup-error', { error: error.message })
        }
      })
    })
  }

  private initializeErrorHandling(): void {
    // 404 handler
    this.app.use((req, res) => {
      res.status(404).json({
        error: 'Not Found',
        message: `Route ${req.originalUrl} not found`,
        timestamp: new Date().toISOString()
      })
    })

    // Global error handler
    this.app.use((error: any, req: express.Request, res: express.Response, next: express.NextFunction) => {
      logger.error('Unhandled error:', error)
      
      res.status(error.status || 500).json({
        error: 'Internal Server Error',
        message: process.env.NODE_ENV === 'development' ? error.message : 'Something went wrong',
        timestamp: new Date().toISOString()
      })
    })
  }

  public async start(): Promise<void> {
    try {
      // Connect to database
      await db.connect()
      logger.info('Database connected successfully')

      // Start server
      this.httpServer.listen(this.port, () => {
        logger.info(`Server running on port ${this.port}`)
        logger.info(`Environment: ${process.env.NODE_ENV}`)
        
        if (process.env.NODE_ENV === 'development') {
          logger.info(`Frontend: http://localhost:5173`)
          logger.info(`Backend: http://localhost:${this.port}`)
        }
      })
    } catch (error) {
      logger.error('Failed to start server:', error)
      process.exit(1)
    }
  }

  public async stop(): Promise<void> {
    try {
      await db.disconnect()
      this.httpServer.close()
      logger.info('Server stopped')
    } catch (error) {
      logger.error('Error stopping server:', error)
    }
  }

  public getIO(): SocketIOServer {
    return this.io
  }
}

// Create and start server
const server = new Server()

// Graceful shutdown
process.on('SIGTERM', async () => {
  logger.info('SIGTERM received, shutting down gracefully')
  await server.stop()
  process.exit(0)
})

process.on('SIGINT', async () => {
  logger.info('SIGINT received, shutting down gracefully')
  await server.stop()
  process.exit(0)
})

// Start the server
server.start().catch((error) => {
  logger.error('Failed to start server:', error)
  process.exit(1)
})

export default server
