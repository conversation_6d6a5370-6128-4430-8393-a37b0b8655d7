import dotenv from 'dotenv'

dotenv.config()

export const config = {
  host: process.env.DB_HOST || 'localhost',
  port: parseInt(process.env.DB_PORT || '3306'),
  user: process.env.DB_USER || 'root',
  password: process.env.DB_PASSWORD || '',
  database: process.env.DB_NAME || 'xui_database',
}

export const serverConfig = {
  port: parseInt(process.env.PORT || '3001'),
  nodeEnv: process.env.NODE_ENV || 'development',
  jwtSecret: process.env.JWT_SECRET || 'your_super_secret_jwt_key_here',
  bcryptRounds: parseInt(process.env.BCRYPT_ROUNDS || '12'),
}

export const tmdbConfig = {
  apiKey: process.env.TMDB_API_KEY || '201066b4b17391d478e55247f43eed64',
  baseUrl: 'https://api.themoviedb.org/3',
  imageBaseUrl: 'https://image.tmdb.org/t/p',
}

export const fileConfig = {
  mediaRootPath: process.env.MEDIA_ROOT_PATH || '/path/to/your/media/files',
  tempUploadPath: process.env.TEMP_UPLOAD_PATH || './temp/uploads',
  maxFileSize: parseInt(process.env.MAX_FILE_SIZE || '1073741824'), // 1GB
}

export const featureFlags = {
  enableFileOperations: process.env.ENABLE_FILE_OPERATIONS === 'true',
  enableDatabaseCleanup: process.env.ENABLE_DATABASE_CLEANUP === 'true',
  enableM3UParsing: process.env.ENABLE_M3U_PARSING === 'true',
}
