#!/usr/bin/env node

import fs from 'fs'
import path from 'path'
import { fileURLToPath } from 'url'

const __filename = fileURLToPath(import.meta.url)
const __dirname = path.dirname(__filename)

console.log('🚀 XUI Multimedia Manager Setup')
console.log('================================\n')

// Check if .env exists
const envPath = path.join(__dirname, '.env')
if (!fs.existsSync(envPath)) {
  console.log('📝 Creating .env file from template...')
  const envExamplePath = path.join(__dirname, '.env.example')
  if (fs.existsSync(envExamplePath)) {
    fs.copyFileSync(envExamplePath, envPath)
    console.log('✅ .env file created successfully')
    console.log('⚠️  Please edit .env file with your database credentials\n')
  } else {
    console.log('❌ .env.example not found\n')
  }
} else {
  console.log('✅ .env file already exists\n')
}

// Create necessary directories
const directories = [
  'logs',
  'temp/uploads',
  'release',
  'assets'
]

console.log('📁 Creating necessary directories...')
directories.forEach(dir => {
  const dirPath = path.join(__dirname, dir)
  if (!fs.existsSync(dirPath)) {
    fs.mkdirSync(dirPath, { recursive: true })
    console.log(`✅ Created: ${dir}`)
  } else {
    console.log(`✅ Exists: ${dir}`)
  }
})

console.log('\n🎯 Next Steps:')
console.log('1. Edit .env file with your database credentials')
console.log('2. Run: npm install')
console.log('3. Set up your MySQL/MariaDB database')
console.log('4. Run: npm run dev')
console.log('\n🌟 Your multimedia manager will be ready!')
console.log('Frontend: http://localhost:5173')
console.log('Backend: http://localhost:3001')

console.log('\n📚 For more information, see README.md')
console.log('🆘 Need help? Check the documentation or create an issue')

console.log('\n✨ Happy organizing! ✨')
