// Database type definitions based on the XUI schema

export interface Stream {
  id: number
  type: number
  category_id: string
  stream_display_name: string
  stream_source: string
  stream_icon: string
  notes: string
  enable_transcode: number
  transcode_attributes: string
  custom_ffmpeg: string
  movie_properties: string
  movie_subtitles: string
  read_native: number
  target_container: string
  stream_all: number
  remove_subtitles: number
  custom_sid: string
  epg_api: number
  epg_id: number
  channel_id: string
  epg_lang: string
  order: number
  auto_restart: string
  transcode_profile_id: number
  gen_timestamps: number
  added: number
  series_no: number
  direct_source: number
  tv_archive_duration: number
  tv_archive_server_id: number
  tv_archive_pid: number
  vframes_server_id: number
  vframes_pid: number
  movie_symlink: number
  rtmp_output: number
  allow_record: number
  probesize_ondemand: number
  custom_map: string
  external_push: string
  delay_minutes: number
  tmdb_language: string
  llod: number
  year: number
  rating: number
  plex_uuid: string
  uuid: string
  epg_offset: number
  updated: Date
  similar: string
  tmdb_id: number
  adaptive_link: string
  title_sync: string
  fps_restart: number
  fps_threshold: number
  direct_proxy: number
}

export interface StreamType {
  type_id: number
  type_name: string
  type_key: string
  type_output: string
  live: number
}

export interface StreamSeries {
  id: number
  title: string
  category_id: string
  cover: string
  cover_big: string
  genre: string
  plot: string
  cast: string
  rating: number
  director: string
  release_date: string
  last_modified: number
  tmdb_id: number
  seasons: string
  episode_run_time: number
  backdrop_path: string
  youtube_trailer: string
  tmdb_language: string
  year: number
  plex_uuid: string
  similar: string
}

export interface StreamEpisode {
  id: number
  season_num: number
  episode_num: number
  series_id: number
  stream_id: number
}

export interface StreamCategory {
  id: number
  category_type: string
  category_name: string
  parent_id: number
  cat_order: number
  is_adult: number
}

export interface StreamServer {
  server_stream_id: number
  stream_id: number
  server_id: number
  parent_id: number
  pid: number
  to_analyze: number
  stream_status: number
  stream_started: number
  stream_info: string
  monitor_pid: number
  aes_pid: number
  current_source: string
  bitrate: number
  progress_info: string
  cc_info: string
  on_demand: number
  delay_pid: number
  delay_available_at: number
  pids_create_channel: string
  cchannel_rsources: string
  updated: Date
  compatible: number
  audio_codec: string
  video_codec: string
  resolution: number
  ondemand_check: number
}

// Extended types for the application
export interface MovieInfo extends Stream {
  type_name: string
  category_name: string
  is_duplicate?: boolean
  duplicate_group?: string
  file_size?: number
  file_path?: string
  quality?: string
}

export interface SeriesInfo extends StreamSeries {
  episode_count: number
  missing_episodes: number[]
  orphan_episodes: StreamEpisode[]
  complete_seasons: number[]
  incomplete_seasons: number[]
}

export interface EpisodeInfo extends StreamEpisode {
  stream_display_name: string
  file_path: string
  file_size: number
  is_duplicate?: boolean
  series_title: string
}

export interface DuplicateGroup {
  id: string
  title: string
  items: MovieInfo[] | EpisodeInfo[]
  total_size: number
  recommended_action: 'keep_4k' | 'keep_symlink' | 'keep_best_quality' | 'manual_review'
}

export interface CleanupStats {
  total_movies: number
  duplicate_movies: number
  total_series: number
  series_without_episodes: number
  orphan_episodes: number
  total_size_duplicates: number
  potential_savings: number
}

export interface M3UEntry {
  title: string
  url: string
  logo?: string
  group?: string
  tvg_id?: string
  tvg_name?: string
  category?: string
  type: 'movie' | 'series' | 'live'
  season?: number
  episode?: number
  year?: number
  tmdb_id?: number
}
