import React from 'react'

interface CategoryData {
  category_name: string
  count: number
  category_type: string
}

interface TopCategoriesChartProps {
  data: CategoryData[]
}

const TopCategoriesChart: React.FC<TopCategoriesChartProps> = ({ data }) => {
  if (!data || data.length === 0) {
    return (
      <div className="flex items-center justify-center h-64 text-vscode-text-muted">
        No categories data available
      </div>
    )
  }

  const maxCount = Math.max(...data.map(item => item.count))
  const sortedData = data.sort((a, b) => b.count - a.count).slice(0, 10)

  return (
    <div className="space-y-3">
      {sortedData.map((item, index) => {
        const percentage = (item.count / maxCount) * 100
        const colors = [
          '#FF00D4', '#D4FF00', '#00D4FF', '#FF6B00', '#6B00FF',
          '#00FF6B', '#FFD400', '#FF0066', '#0066FF', '#66FF00'
        ]
        const color = colors[index % colors.length]
        
        return (
          <div key={`${item.category_name}-${index}`} className="flex items-center space-x-3">
            <div className="w-24 text-xs text-vscode-text-muted text-right">
              {item.category_name.length > 12 
                ? `${item.category_name.substring(0, 12)}...` 
                : item.category_name
              }
            </div>
            <div className="flex-1 bg-vscode-border rounded-full h-4 relative overflow-hidden">
              <div
                className="h-full rounded-full transition-all duration-500"
                style={{
                  width: `${percentage}%`,
                  backgroundColor: color
                }}
              />
              <div className="absolute inset-0 flex items-center justify-end pr-2">
                <span className="text-xs font-mono text-white">
                  {item.count.toLocaleString()}
                </span>
              </div>
            </div>
          </div>
        )
      })}
    </div>
  )
}

export default TopCategoriesChart
