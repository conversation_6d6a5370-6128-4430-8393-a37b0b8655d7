@tailwind base;
@tailwind components;
@tailwind utilities;

/* Custom scrollbar styles for VS Code look */
@layer base {
  * {
    scrollbar-width: thin;
    scrollbar-color: #424242 #1e1e1e;
  }

  *::-webkit-scrollbar {
    width: 14px;
    height: 14px;
  }

  *::-webkit-scrollbar-track {
    background: #1e1e1e;
  }

  *::-webkit-scrollbar-thumb {
    background: #424242;
    border-radius: 0;
    border: 3px solid #1e1e1e;
  }

  *::-webkit-scrollbar-thumb:hover {
    background: #4f4f4f;
  }

  *::-webkit-scrollbar-corner {
    background: #1e1e1e;
  }

  /* Selection colors */
  ::selection {
    background: #264f78;
    color: #ffffff;
  }

  ::-moz-selection {
    background: #264f78;
    color: #ffffff;
  }
}

@layer components {
  /* VS Code-like button styles */
  .btn-primary {
    @apply bg-vscode-accent hover:bg-vscode-accent-hover text-white px-4 py-2 rounded-sm font-medium transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-vscode-accent focus:ring-opacity-50;
  }

  .btn-secondary {
    @apply bg-vscode-panel hover:bg-vscode-border text-vscode-text px-4 py-2 rounded-sm font-medium transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-vscode-border focus:ring-opacity-50;
  }

  .btn-danger {
    @apply bg-vscode-error hover:bg-red-600 text-white px-4 py-2 rounded-sm font-medium transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-vscode-error focus:ring-opacity-50;
  }

  /* Input styles */
  .input-primary {
    @apply bg-vscode-panel border border-vscode-border text-vscode-text px-3 py-2 rounded-sm focus:outline-none focus:ring-2 focus:ring-vscode-accent focus:border-transparent placeholder-vscode-text-muted;
  }

  /* Card styles */
  .card {
    @apply bg-vscode-panel border border-vscode-border rounded-sm shadow-vscode;
  }

  .card-header {
    @apply px-4 py-3 border-b border-vscode-border;
  }

  .card-body {
    @apply p-4;
  }

  /* Sidebar styles */
  .sidebar-item {
    @apply flex items-center px-3 py-2 text-sm text-vscode-text hover:bg-vscode-border cursor-pointer transition-colors duration-150;
  }

  .sidebar-item.active {
    @apply bg-vscode-border text-white;
  }

  /* Table styles */
  .table {
    @apply w-full border-collapse;
  }

  .table th {
    @apply bg-vscode-panel text-left px-4 py-3 text-sm font-medium text-vscode-text border-b border-vscode-border;
  }

  .table td {
    @apply px-4 py-3 text-sm text-vscode-text border-b border-vscode-border;
  }

  .table tr:hover {
    @apply bg-vscode-border;
  }

  /* Status indicators */
  .status-online {
    @apply inline-block w-2 h-2 bg-vscode-success rounded-full;
  }

  .status-offline {
    @apply inline-block w-2 h-2 bg-vscode-error rounded-full;
  }

  .status-warning {
    @apply inline-block w-2 h-2 bg-vscode-warning rounded-full;
  }

  /* Loading spinner */
  .spinner {
    @apply inline-block w-4 h-4 border-2 border-vscode-text-muted border-t-vscode-accent rounded-full animate-spin;
  }

  /* Chart container */
  .chart-container {
    @apply bg-vscode-panel border border-vscode-border rounded-sm p-4;
  }

  /* Modal styles */
  .modal-overlay {
    @apply fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50;
  }

  .modal-content {
    @apply bg-vscode-panel border border-vscode-border rounded-sm shadow-lg max-w-lg w-full mx-4;
  }

  /* Notification styles */
  .notification {
    @apply fixed top-4 right-4 bg-vscode-panel border border-vscode-border rounded-sm shadow-lg p-4 z-40 max-w-sm;
  }

  .notification.success {
    @apply border-vscode-success;
  }

  .notification.error {
    @apply border-vscode-error;
  }

  .notification.warning {
    @apply border-vscode-warning;
  }

  .notification.info {
    @apply border-vscode-info;
  }
}

/* Custom animations */
@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes slideUp {
  from { 
    opacity: 0;
    transform: translateY(10px);
  }
  to { 
    opacity: 1;
    transform: translateY(0);
  }
}

.animate-fade-in {
  animation: fadeIn 0.5s ease-in-out;
}

.animate-slide-up {
  animation: slideUp 0.3s ease-out;
}

/* Typography */
.text-mono {
  font-family: 'Fira Code', 'Monaco', 'Cascadia Code', 'Roboto Mono', monospace;
}

/* Layout utilities */
.layout-container {
  @apply h-screen flex flex-col bg-vscode-bg;
}

.layout-main {
  @apply flex-1 flex overflow-hidden;
}

.layout-sidebar {
  @apply w-64 bg-vscode-sidebar border-r border-vscode-border flex-shrink-0;
}

.layout-content {
  @apply flex-1 overflow-auto;
}
